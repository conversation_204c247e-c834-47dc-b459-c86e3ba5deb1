/* Organic Design Overrides for MealMind */

/* Hide old floating elements */
.bg-elements,
.floating-shape {
    display: none !important;
}

/* Override any remaining old styles */
.modern-header,
.nav-container,
.nav-brand,
.nav-menu,
.hero-section,
.hero-content,
.ingredient-finder-section,
.method-card,
.quick-actions-section,
.features-section {
    all: unset !important;
    display: block !important;
}

/* Ensure Tailwind styles take precedence */
* {
    box-sizing: border-box;
}

/* Custom animations for organic feel */
@keyframes gentle-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.animate-gentle-bounce {
    animation: gentle-bounce 2s infinite;
}

/* Smooth transitions for organic feel */
.transition-organic {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom focus styles */
.focus-organic:focus {
    outline: none;
    ring: 2px;
    ring-color: #1A5E2A;
    ring-opacity: 0.3;
    border-color: #1A5E2A;
}

/* Recipe card hover effects */
.recipe-card-organic {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recipe-card-organic:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(26, 94, 42, 0.15);
}

/* Ingredient tag styles */
.ingredient-tag {
    background: #E8F5E8;
    color: #1A5E2A;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-block;
    margin: 0.125rem;
}

/* Custom scrollbar for organic feel */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #F9F5ED;
}

::-webkit-scrollbar-thumb {
    background: #1A5E2A;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2D7A3D;
}

/* Notification styles */
.notification {
    background: white;
    border: 1px solid #E8F5E8;
    border-left: 4px solid #1A5E2A;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading states */
.loading-organic {
    background: linear-gradient(90deg, #F9F5ED 25%, #E8F5E8 50%, #F9F5ED 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
    .mobile-stack {
        flex-direction: column !important;
    }
    
    .mobile-full {
        width: 100% !important;
    }
    
    .mobile-center {
        text-align: center !important;
    }
}

/* Print styles for recipes */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .print-friendly {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: #F9F5ED;
    margin: 10% auto;
    padding: 2rem;
    border: none;
    border-radius: 1rem;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #1A5E2A;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover,
.close:focus {
    color: #2D7A3D;
    text-decoration: none;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #1A5E2A;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #1A5E2A;
    border-radius: 0.5rem;
    background-color: #FDF9F0;
    color: #1A5E2A;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #2D7A3D;
    box-shadow: 0 0 0 3px rgba(26, 94, 42, 0.1);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #1A5E2A;
    color: #F9F5ED;
}

.btn-primary:hover {
    background-color: #2D7A3D;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(26, 94, 42, 0.3);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Meal Plan specific styles */
.quick-recipe-card {
    background: white;
    border: 1px solid #E8F5E8;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.quick-recipe-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(26, 94, 42, 0.15);
    border-color: #1A5E2A;
}

.quick-recipe-card h4 {
    color: #1A5E2A;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.recipe-info {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #6B7280;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6B7280;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.weekly-view {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid #E8F5E8;
}

.week-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.day-column {
    background: #F9F5ED;
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid #E8F5E8;
}

.day-column h3 {
    color: #1A5E2A;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.meal-slot {
    margin-bottom: 1rem;
}

.meal-slot h4 {
    color: #2D7A3D;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.planned-meal {
    background: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    border: 1px solid #E8F5E8;
}

.empty-meal {
    color: #9CA3AF;
    font-style: italic;
    font-size: 0.875rem;
}
