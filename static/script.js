// Enhanced JavaScript for MealMind

// Global variables
let currentRecipeId = null;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize modal functionality
    initializeModal();

    // Set default date to today
    const dateInput = document.getElementById('date');
    if (dateInput) {
        dateInput.value = new Date().toISOString().split('T')[0];
    }

    // Initialize image upload functionality
    initializeImageUpload();

    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Add loading states to buttons
    addLoadingStates();

    // Initialize search functionality
    initializeSearch();

    // Initialize advanced search
    initializeAdvancedSearch();
}

function initializeModal() {
    const modal = document.getElementById('meal-plan-modal');
    const closeBtn = document.querySelector('.close');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal.style.display === 'block') {
            modal.style.display = 'none';
        }
    });
}

function addToMealPlan(recipeId) {
    currentRecipeId = recipeId;
    const modal = document.getElementById('meal-plan-modal');
    const recipeIdInput = document.getElementById('modal-recipe-id');
    const dateInput = document.getElementById('date');

    if (recipeIdInput) {
        recipeIdInput.value = recipeId;
    }

    // Set today's date as default
    if (dateInput && !dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }

    modal.style.display = 'block';

    // Add animation class
    const modalContent = modal.querySelector('.modal-content');
    modalContent.style.animation = 'modalSlideIn 0.3s ease';
}

function initializeImageUpload() {
    const uploadArea = document.getElementById('image-upload-area');
    const fileInput = document.getElementById('ingredients_image');
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    const placeholder = uploadArea.querySelector('.upload-placeholder');

    if (!uploadArea || !fileInput) return;

    // Click to upload
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#667eea';
        uploadArea.style.backgroundColor = '#edf2f7';
    });

    uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#e2e8f0';
        uploadArea.style.backgroundColor = '#f7fafc';
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#e2e8f0';
        uploadArea.style.backgroundColor = '#f7fafc';

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleImagePreview(files[0]);
        }
    });

    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleImagePreview(e.target.files[0]);
        }
    });

    function handleImagePreview(file) {
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImg.src = e.target.result;
                placeholder.style.display = 'none';
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    }
}

function removeImage() {
    const fileInput = document.getElementById('ingredients_image');
    const preview = document.getElementById('image-preview');
    const placeholder = document.querySelector('.upload-placeholder');

    fileInput.value = '';
    preview.style.display = 'none';
    placeholder.style.display = 'flex';
}

function initializeAdvancedSearch() {
    const cuisineSelect = document.getElementById('browse-cuisine');
    const dietSelect = document.getElementById('browse-diet');

    if (cuisineSelect) {
        cuisineSelect.addEventListener('change', searchRecipes);
    }
    if (dietSelect) {
        dietSelect.addEventListener('change', searchRecipes);
    }
}

function searchRecipes() {
    const searchInput = document.getElementById('search-input');
    const cuisineSelect = document.getElementById('browse-cuisine');
    const dietSelect = document.getElementById('browse-diet');

    const query = searchInput ? searchInput.value.trim() : '';
    const cuisine = cuisineSelect ? cuisineSelect.value : '';
    const dietType = dietSelect ? dietSelect.value : '';

    // Show loading state
    const recipesContainer = document.getElementById('recipes-container');
    if (recipesContainer) {
        recipesContainer.innerHTML = '<div class="loading-container"><div class="loading"></div><p>Searching recipes...</p></div>';
    }

    // Build query parameters
    const params = new URLSearchParams();
    if (query) params.append('q', query);
    if (cuisine) params.append('cuisine', cuisine);
    if (dietType) params.append('diet_type', dietType);

    fetch(`/search-recipes?${params.toString()}`)
        .then(response => response.json())
        .then(recipes => {
            displaySearchResults(recipes);
        })
        .catch(error => {
            console.error('Error searching recipes:', error);
            if (recipesContainer) {
                recipesContainer.innerHTML = '<p class="error-message">Error searching recipes. Please try again.</p>';
            }
        });
}

function displaySearchResults(recipes) {
    const recipesContainer = document.getElementById('recipes-container');

    if (recipes.length === 0) {
        recipesContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">🔍</div>
                <h3>No recipes found</h3>
                <p>Try searching with different keywords or adjust your filters</p>
                <button onclick="location.reload()" class="btn btn-primary">Show All Recipes</button>
            </div>
        `;
        return;
    }

    let html = '';
    recipes.forEach(recipe => {
        const ingredientsList = recipe.ingredients.slice(0, 4).map(ing => `<li>${ing}</li>`).join('');
        const moreIngredients = recipe.ingredients.length > 4 ? `<li>... and ${recipe.ingredients.length - 4} more</li>` : '';

        // Get diet type class for styling
        const dietClass = recipe.diet_type.toLowerCase().replace('-', '').replace(' ', '');

        html += `
            <div class="recipe-card">
                <div class="recipe-header">
                    <h3>${recipe.name}</h3>
                    <div class="recipe-badges">
                        <span class="cuisine-badge">${recipe.cuisine}</span>
                        <span class="diet-badge diet-${dietClass}">${recipe.diet_type}</span>
                    </div>
                </div>
                <div class="recipe-info">
                    <span class="prep-time">⏱️ ${recipe.prep_time} min</span>
                    <span class="servings">👥 ${recipe.servings} servings</span>
                    <span class="calories">🔥 ${recipe.calories} cal</span>
                    <span class="difficulty">📊 ${recipe.difficulty}</span>
                </div>
                <div class="ingredients">
                    <strong>🥘 Key Ingredients:</strong>
                    <ul>
                        ${ingredientsList}
                        ${moreIngredients}
                    </ul>
                </div>
                <div class="recipe-actions">
                    <a href="/recipe/${recipe.id}" class="btn btn-primary">👁️ View Recipe</a>
                    <button onclick="addToMealPlan(${recipe.id})" class="btn btn-secondary">➕ Add to Plan</button>
                </div>
            </div>
        `;
    });

    recipesContainer.innerHTML = html;

    // Add stagger animation to cards
    const cards = recipesContainer.querySelectorAll('.recipe-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        // Add Enter key support
        searchInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                searchRecipes();
            }
        });
        
        // Add real-time search with debouncing
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.trim().length > 2) {
                    searchRecipes();
                } else if (this.value.trim().length === 0) {
                    location.reload();
                }
            }, 500);
        });
    }
}

function addLoadingStates() {
    // Add loading states to form submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.textContent;
                submitBtn.innerHTML = '<div class="loading"></div> Adding...';
                submitBtn.disabled = true;
                
                // Re-enable after 3 seconds (fallback)
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }, 3000);
            }
        });
    });
}

function loadWeeklyPlan() {
    const weeklyView = document.getElementById('weekly-view');
    const weekGrid = document.getElementById('week-grid');
    
    // Show loading state
    weekGrid.innerHTML = '<div class="loading-container"><div class="loading"></div><p>Loading weekly plan...</p></div>';
    weeklyView.style.display = 'block';
    
    fetch('/weekly-meal-plan')
        .then(response => response.json())
        .then(data => {
            let html = '';
            for (const [date, meals] of Object.entries(data)) {
                const dateObj = new Date(date);
                const dayName = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
                const dayDate = dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                
                html += `
                    <div class="day-column">
                        <h3>${dayName}<br><small>${dayDate}</small></h3>
                        <div class="day-meals">
                            <div class="meal-slot">
                                <h4>🌅 Breakfast</h4>
                                ${meals.breakfast.map(recipe => `<div class="planned-meal">${recipe.name}</div>`).join('') || '<div class="empty-meal">No meal planned</div>'}
                            </div>
                            <div class="meal-slot">
                                <h4>☀️ Lunch</h4>
                                ${meals.lunch.map(recipe => `<div class="planned-meal">${recipe.name}</div>`).join('') || '<div class="empty-meal">No meal planned</div>'}
                            </div>
                            <div class="meal-slot">
                                <h4>🌙 Dinner</h4>
                                ${meals.dinner.map(recipe => `<div class="planned-meal">${recipe.name}</div>`).join('') || '<div class="empty-meal">No meal planned</div>'}
                            </div>
                        </div>
                    </div>
                `;
            }
            weekGrid.innerHTML = html;
            
            // Add animation to day columns
            const dayColumns = weekGrid.querySelectorAll('.day-column');
            dayColumns.forEach((column, index) => {
                column.style.opacity = '0';
                column.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    column.style.transition = 'all 0.5s ease';
                    column.style.opacity = '1';
                    column.style.transform = 'translateY(0)';
                }, index * 100);
            });
        })
        .catch(error => {
            console.error('Error loading weekly plan:', error);
            weekGrid.innerHTML = '<p class="error-message">Error loading weekly plan. Please try again.</p>';
        });
}

function showAddMealModal() {
    const modal = document.getElementById('meal-plan-modal');
    modal.style.display = 'block';
}

function removeMealPlan(date, mealType) {
    if (!confirm(`Are you sure you want to remove this ${mealType} from ${date}?`)) {
        return;
    }

    fetch('/remove-meal-plan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            date: date,
            meal_type: mealType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Reload the page to update the meal plan display
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.error || 'Failed to remove meal plan', 'error');
        }
    })
    .catch(error => {
        console.error('Error removing meal plan:', error);
        showNotification('Error removing meal plan. Please try again.', 'error');
    });
}

function submitMealPlan(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);

    const data = {
        recipe_id: formData.get('recipe_id'),
        date: formData.get('date'),
        meal_type: formData.get('meal_type')
    };

    fetch('/add-to-meal-plan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Close the modal
            document.getElementById('meal-plan-modal').style.display = 'none';
            // Reset the form
            form.reset();
            // Reload the page to update the meal plan display
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.error || 'Failed to add meal to plan', 'error');
        }
    })
    .catch(error => {
        console.error('Error adding meal to plan:', error);
        showNotification('Error adding meal to plan. Please try again.', 'error');
    });
}

// Utility functions
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 2rem;
        background: ${type === 'success' ? '#48bb78' : '#f56565'};
        color: white;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        z-index: 1001;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add smooth page transitions
window.addEventListener('beforeunload', function() {
    document.body.style.opacity = '0.5';
});

// Favorites functionality
function toggleFavorite(recipeId) {
    fetch('/toggle-favorite', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ recipe_id: recipeId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showNotification(data.error, 'error');
            return;
        }

        // Update heart icon
        const heartButtons = document.querySelectorAll(`[onclick="toggleFavorite(${recipeId})"]`);
        heartButtons.forEach(btn => {
            if (data.is_favorite) {
                btn.innerHTML = '❤️';
                btn.classList.add('favorited');
            } else {
                btn.innerHTML = '🤍';
                btn.classList.remove('favorited');
            }
        });

        showNotification(data.message, 'success');
    })
    .catch(error => {
        console.error('Error toggling favorite:', error);
        showNotification('Error updating favorite', 'error');
    });
}

// Enhanced image upload with ingredient detection simulation
function handleImageUpload(file) {
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        const previewImg = document.getElementById('preview-img');
        const placeholder = document.getElementById('upload-placeholder');
        const preview = document.getElementById('image-preview');
        const detectedSection = document.getElementById('detected-ingredients');

        previewImg.src = e.target.result;
        placeholder.style.display = 'none';
        preview.style.display = 'block';

        // Simulate AI ingredient detection
        setTimeout(() => {
            const simulatedIngredients = simulateIngredientDetection();
            displayDetectedIngredients(simulatedIngredients);
            detectedSection.style.display = 'block';
        }, 2000);
    };
    reader.readAsDataURL(file);
}

function simulateIngredientDetection() {
    const commonIngredients = [
        'tomatoes', 'onions', 'garlic', 'bell peppers', 'carrots', 'potatoes',
        'chicken', 'beef', 'eggs', 'cheese', 'rice', 'pasta', 'bread',
        'spinach', 'mushrooms', 'broccoli', 'lemon', 'herbs'
    ];

    const numIngredients = Math.floor(Math.random() * 5) + 3; // 3-7 ingredients
    const detected = [];

    for (let i = 0; i < numIngredients; i++) {
        const randomIndex = Math.floor(Math.random() * commonIngredients.length);
        const ingredient = commonIngredients[randomIndex];
        if (!detected.includes(ingredient)) {
            detected.push(ingredient);
        }
    }

    return detected;
}

function displayDetectedIngredients(ingredients) {
    const tagsContainer = document.getElementById('detected-tags');
    const textInput = document.getElementById('ingredients_text');

    tagsContainer.innerHTML = '';
    ingredients.forEach(ingredient => {
        const tag = document.createElement('span');
        tag.className = 'ingredient-tag detected';
        tag.textContent = ingredient;
        tagsContainer.appendChild(tag);
    });

    // Also populate the text input
    textInput.value = ingredients.join(', ');
}

// Enhanced form validation
function validateIngredientForm() {
    const imageInput = document.getElementById('ingredients_image');
    const textInput = document.getElementById('ingredients_text');

    if (!imageInput.files.length && !textInput.value.trim()) {
        showNotification('Please provide ingredients either by uploading an image or typing them in.', 'error');
        return false;
    }

    return true;
}

// Add intersection observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Enhanced initialization
document.addEventListener('DOMContentLoaded', function() {
    // Animate elements
    const animateElements = document.querySelectorAll('.recipe-card, .meal-item, .quick-recipe-card, .method-card');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });

    // Add form validation
    const ingredientForm = document.querySelector('.modern-ingredient-form');
    if (ingredientForm) {
        ingredientForm.addEventListener('submit', function(e) {
            if (!validateIngredientForm()) {
                e.preventDefault();
            }
        });
    }

    // Add favorite buttons to existing recipe cards
    addFavoriteButtons();
});

function addFavoriteButtons() {
    const recipeCards = document.querySelectorAll('.recipe-card');
    recipeCards.forEach(card => {
        const actions = card.querySelector('.recipe-actions');
        if (actions && !actions.querySelector('.favorite-btn')) {
            const recipeId = extractRecipeId(card);
            if (recipeId) {
                const favoriteBtn = document.createElement('button');
                favoriteBtn.className = 'btn btn-secondary favorite-btn';
                favoriteBtn.innerHTML = '🤍';
                favoriteBtn.onclick = () => toggleFavorite(recipeId);
                actions.appendChild(favoriteBtn);
            }
        }
    });
}

function extractRecipeId(card) {
    const viewLink = card.querySelector('a[href*="/recipe/"]');
    if (viewLink) {
        const href = viewLink.getAttribute('href');
        const match = href.match(/\/recipe\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    }
    return null;
}
