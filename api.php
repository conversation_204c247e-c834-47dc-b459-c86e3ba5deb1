<?php
/**
 * MealMind PHP API Backend
 * Provides additional backend functionality for the MealMind application
 */

// Enable CORS for cross-origin requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
$db_config = [
    'host' => 'localhost',
    'dbname' => 'mealmind',
    'username' => 'root',
    'password' => ''
];

// Database connection
try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset=utf8mb4",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    // If database connection fails, use file-based storage
    $pdo = null;
}

// Utility functions
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data);
    exit();
}

function sendError($message, $status = 400) {
    sendResponse(['error' => $message, 'success' => false], $status);
}

function getJsonInput() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

// File-based storage functions (fallback when no database)
function saveToFile($filename, $data) {
    $filepath = __DIR__ . '/data/' . $filename;
    if (!is_dir(dirname($filepath))) {
        mkdir(dirname($filepath), 0755, true);
    }
    return file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
}

function loadFromFile($filename) {
    $filepath = __DIR__ . '/data/' . $filename;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        return json_decode($content, true) ?: [];
    }
    return [];
}

// Route handling
$request_uri = $_SERVER['REQUEST_URI'];
$request_method = $_SERVER['REQUEST_METHOD'];

// Remove query string and get path
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/api.php', '', $path);

// API Routes
switch ($path) {
    case '/nutrition-analysis':
        handleNutritionAnalysis();
        break;
    
    case '/recipe-suggestions':
        handleRecipeSuggestions();
        break;
    
    case '/user-preferences':
        handleUserPreferences();
        break;
    
    case '/recipe-ratings':
        handleRecipeRatings();
        break;
    
    case '/cooking-tips':
        handleCookingTips();
        break;
    
    case '/ingredient-substitutions':
        handleIngredientSubstitutions();
        break;
    
    default:
        sendError('Endpoint not found', 404);
}

/**
 * Nutrition Analysis API
 * Analyzes nutritional content of recipes
 */
function handleNutritionAnalysis() {
    global $request_method;
    
    if ($request_method !== 'POST') {
        sendError('Method not allowed', 405);
    }
    
    $input = getJsonInput();
    $ingredients = $input['ingredients'] ?? [];
    $servings = $input['servings'] ?? 1;
    
    if (empty($ingredients)) {
        sendError('Ingredients are required');
    }
    
    // Nutrition database (simplified)
    $nutrition_db = [
        'chicken' => ['calories' => 165, 'protein' => 31, 'carbs' => 0, 'fat' => 3.6],
        'rice' => ['calories' => 130, 'protein' => 2.7, 'carbs' => 28, 'fat' => 0.3],
        'tomato' => ['calories' => 18, 'protein' => 0.9, 'carbs' => 3.9, 'fat' => 0.2],
        'onion' => ['calories' => 40, 'protein' => 1.1, 'carbs' => 9.3, 'fat' => 0.1],
        'oil' => ['calories' => 884, 'protein' => 0, 'carbs' => 0, 'fat' => 100],
        'garlic' => ['calories' => 149, 'protein' => 6.4, 'carbs' => 33, 'fat' => 0.5]
    ];
    
    $total_nutrition = ['calories' => 0, 'protein' => 0, 'carbs' => 0, 'fat' => 0];
    
    foreach ($ingredients as $ingredient) {
        $name = strtolower(trim($ingredient));
        
        // Extract quantity and unit (simplified)
        preg_match('/(\d+(?:\.\d+)?)\s*(\w+)?\s+(.+)/', $ingredient, $matches);
        $quantity = isset($matches[1]) ? floatval($matches[1]) : 1;
        $unit = isset($matches[2]) ? $matches[2] : 'unit';
        $ingredient_name = isset($matches[3]) ? trim($matches[3]) : $name;
        
        // Find matching ingredient in database
        foreach ($nutrition_db as $db_ingredient => $nutrition) {
            if (strpos($ingredient_name, $db_ingredient) !== false) {
                $multiplier = $quantity / 100; // Assuming per 100g values
                
                foreach ($nutrition as $nutrient => $value) {
                    $total_nutrition[$nutrient] += $value * $multiplier;
                }
                break;
            }
        }
    }
    
    // Calculate per serving
    foreach ($total_nutrition as $nutrient => $value) {
        $total_nutrition[$nutrient] = round($value / $servings, 1);
    }
    
    sendResponse([
        'success' => true,
        'nutrition' => $total_nutrition,
        'servings' => $servings,
        'per_serving' => true
    ]);
}

/**
 * Recipe Suggestions API
 * Provides personalized recipe suggestions
 */
function handleRecipeSuggestions() {
    global $request_method;
    
    if ($request_method !== 'GET') {
        sendError('Method not allowed', 405);
    }
    
    $cuisine = $_GET['cuisine'] ?? 'any';
    $diet = $_GET['diet'] ?? 'any';
    $time = intval($_GET['time'] ?? 60);
    $difficulty = $_GET['difficulty'] ?? 'any';
    
    // Sample recipe suggestions
    $recipes = [
        [
            'id' => 1001,
            'name' => 'Quick Chicken Stir Fry',
            'cuisine' => 'Chinese',
            'diet' => 'Regular',
            'prep_time' => 20,
            'difficulty' => 'Easy',
            'calories' => 320,
            'rating' => 4.5,
            'description' => 'A quick and healthy chicken stir fry with vegetables'
        ],
        [
            'id' => 1002,
            'name' => 'Vegetarian Pasta Primavera',
            'cuisine' => 'Italian',
            'diet' => 'Vegetarian',
            'prep_time' => 25,
            'difficulty' => 'Easy',
            'calories' => 280,
            'rating' => 4.3,
            'description' => 'Fresh vegetables with pasta in a light sauce'
        ],
        [
            'id' => 1003,
            'name' => 'Spicy Lentil Curry',
            'cuisine' => 'Indian',
            'diet' => 'Vegan',
            'prep_time' => 35,
            'difficulty' => 'Medium',
            'calories' => 250,
            'rating' => 4.7,
            'description' => 'Protein-rich lentils in aromatic spices'
        ]
    ];
    
    // Filter recipes based on criteria
    $filtered_recipes = array_filter($recipes, function($recipe) use ($cuisine, $diet, $time, $difficulty) {
        if ($cuisine !== 'any' && strtolower($recipe['cuisine']) !== strtolower($cuisine)) {
            return false;
        }
        if ($diet !== 'any' && strtolower($recipe['diet']) !== strtolower($diet)) {
            return false;
        }
        if ($recipe['prep_time'] > $time) {
            return false;
        }
        if ($difficulty !== 'any' && strtolower($recipe['difficulty']) !== strtolower($difficulty)) {
            return false;
        }
        return true;
    });
    
    sendResponse([
        'success' => true,
        'recipes' => array_values($filtered_recipes),
        'total' => count($filtered_recipes),
        'filters' => [
            'cuisine' => $cuisine,
            'diet' => $diet,
            'max_time' => $time,
            'difficulty' => $difficulty
        ]
    ]);
}

/**
 * User Preferences API
 * Manages user cooking preferences
 */
function handleUserPreferences() {
    global $request_method, $pdo;
    
    if ($request_method === 'GET') {
        $user_id = $_GET['user_id'] ?? null;
        if (!$user_id) {
            sendError('User ID is required');
        }
        
        $preferences = loadFromFile("preferences_{$user_id}.json");
        sendResponse(['success' => true, 'preferences' => $preferences]);
        
    } elseif ($request_method === 'POST') {
        $input = getJsonInput();
        $user_id = $input['user_id'] ?? null;
        $preferences = $input['preferences'] ?? [];
        
        if (!$user_id) {
            sendError('User ID is required');
        }
        
        // Save preferences
        saveToFile("preferences_{$user_id}.json", $preferences);
        
        sendResponse([
            'success' => true,
            'message' => 'Preferences saved successfully',
            'preferences' => $preferences
        ]);
    } else {
        sendError('Method not allowed', 405);
    }
}

/**
 * Recipe Ratings API
 * Handles recipe ratings and reviews
 */
function handleRecipeRatings() {
    global $request_method;
    
    if ($request_method === 'POST') {
        $input = getJsonInput();
        $recipe_id = $input['recipe_id'] ?? null;
        $user_id = $input['user_id'] ?? null;
        $rating = $input['rating'] ?? null;
        $review = $input['review'] ?? '';
        
        if (!$recipe_id || !$user_id || !$rating) {
            sendError('Recipe ID, User ID, and rating are required');
        }
        
        if ($rating < 1 || $rating > 5) {
            sendError('Rating must be between 1 and 5');
        }
        
        // Load existing ratings
        $ratings = loadFromFile('ratings.json');
        
        // Add new rating
        $ratings[] = [
            'recipe_id' => $recipe_id,
            'user_id' => $user_id,
            'rating' => $rating,
            'review' => $review,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Save ratings
        saveToFile('ratings.json', $ratings);
        
        sendResponse([
            'success' => true,
            'message' => 'Rating submitted successfully'
        ]);
        
    } elseif ($request_method === 'GET') {
        $recipe_id = $_GET['recipe_id'] ?? null;
        
        if (!$recipe_id) {
            sendError('Recipe ID is required');
        }
        
        $ratings = loadFromFile('ratings.json');
        $recipe_ratings = array_filter($ratings, function($rating) use ($recipe_id) {
            return $rating['recipe_id'] == $recipe_id;
        });
        
        $avg_rating = 0;
        $total_ratings = count($recipe_ratings);
        
        if ($total_ratings > 0) {
            $sum = array_sum(array_column($recipe_ratings, 'rating'));
            $avg_rating = round($sum / $total_ratings, 1);
        }
        
        sendResponse([
            'success' => true,
            'average_rating' => $avg_rating,
            'total_ratings' => $total_ratings,
            'ratings' => array_values($recipe_ratings)
        ]);
    } else {
        sendError('Method not allowed', 405);
    }
}

/**
 * Cooking Tips API
 * Provides cooking tips and techniques
 */
function handleCookingTips() {
    global $request_method;
    
    if ($request_method !== 'GET') {
        sendError('Method not allowed', 405);
    }
    
    $category = $_GET['category'] ?? 'general';
    
    $tips = [
        'general' => [
            'Always read the entire recipe before starting to cook',
            'Mise en place - prepare all ingredients before cooking',
            'Taste as you go and adjust seasoning accordingly',
            'Use a sharp knife for safer and more efficient cutting'
        ],
        'baking' => [
            'Measure ingredients accurately for best results',
            'Preheat your oven to the correct temperature',
            'Don\'t overmix cake batter to avoid tough texture',
            'Use room temperature ingredients unless specified otherwise'
        ],
        'grilling' => [
            'Preheat the grill for even cooking',
            'Oil the grates to prevent sticking',
            'Don\'t flip meat too frequently',
            'Let meat rest after grilling for juicier results'
        ]
    ];
    
    $selected_tips = $tips[$category] ?? $tips['general'];
    
    sendResponse([
        'success' => true,
        'category' => $category,
        'tips' => $selected_tips
    ]);
}

/**
 * Ingredient Substitutions API
 * Provides ingredient substitution suggestions
 */
function handleIngredientSubstitutions() {
    global $request_method;
    
    if ($request_method !== 'GET') {
        sendError('Method not allowed', 405);
    }
    
    $ingredient = $_GET['ingredient'] ?? '';
    
    if (empty($ingredient)) {
        sendError('Ingredient parameter is required');
    }
    
    $substitutions = [
        'butter' => [
            'oil' => '3/4 the amount of vegetable oil',
            'applesauce' => 'Equal amount for baking',
            'margarine' => 'Equal amount'
        ],
        'eggs' => [
            'flax_egg' => '1 tbsp ground flaxseed + 3 tbsp water per egg',
            'applesauce' => '1/4 cup per egg for baking',
            'banana' => '1/4 cup mashed banana per egg'
        ],
        'milk' => [
            'almond_milk' => 'Equal amount',
            'coconut_milk' => 'Equal amount',
            'water_powder' => '1 cup water + 1/3 cup powdered milk'
        ]
    ];
    
    $ingredient_lower = strtolower(trim($ingredient));
    $found_substitutions = [];
    
    foreach ($substitutions as $key => $subs) {
        if (strpos($ingredient_lower, $key) !== false) {
            $found_substitutions = $subs;
            break;
        }
    }
    
    sendResponse([
        'success' => true,
        'ingredient' => $ingredient,
        'substitutions' => $found_substitutions,
        'found' => !empty($found_substitutions)
    ]);
}

?>
