from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
import os
import json
from datetime import datetime, timedelta
import requests
from werkzeug.utils import secure_filename
import base64
import uuid
import hashlib
from ingredient_ml import identify_ingredients, get_ingredient_suggestions
try:
    from PIL import Image
    import io
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

app = Flask(__name__)
app.secret_key = 'your-secret-key-here-change-in-production'

# Simple in-memory storage (in production, use a proper database)
users = {}
user_favorites = {}
user_profiles = {}

# Create uploads directory if it doesn't exist
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Allowed file extensions for image upload
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Comprehensive recipe database with 4 main cuisines (50 recipes each)
recipes = [
    # ITALIAN CUISINE (50 recipes)
    {
        'id': 1,
        'name': 'Spaghetti Carbonara',
        'ingredients': {
            'main': ['400g spaghetti pasta', '4 large eggs', '150g pancetta'],
            'dairy': ['100g parmesan cheese (grated)', '2 tbsp heavy cream'],
            'aromatics': ['3 cloves garlic (minced)', '1 medium onion (diced)'],
            'seasonings': ['1 tsp black pepper', '1 tsp salt', '2 tbsp olive oil'],
            'garnish': ['fresh parsley', 'extra parmesan']
        },
        'instructions': [
            'Bring salted water to boil and cook spaghetti until al dente (8-10 minutes)',
            'Heat olive oil in large skillet, add pancetta and cook until crispy (5-7 minutes)',
            'Add garlic and onion, sauté until fragrant (1-2 minutes)',
            'Whisk eggs with parmesan, black pepper, and salt in a bowl',
            'Reserve 1 cup pasta water before draining',
            'Add hot pasta to skillet with pancetta, remove from heat',
            'Quickly mix in egg mixture, tossing continuously for creamy sauce',
            'Add pasta water if needed, serve with parmesan and parsley'
        ],
        'prep_time': 30,
        'servings': 4,
        'calories': 450,
        'cuisine': 'Italian',
        'diet_type': 'Non-Veg',
        'difficulty': 'Medium',
        'tips': 'Work quickly with eggs to prevent scrambling. Use residual heat to cook eggs gently.'
    },
    {
        'id': 2,
        'name': 'Margherita Pizza',
        'ingredients': {
            'main': ['1 pizza dough', '200g mozzarella cheese', '3 large tomatoes'],
            'dairy': ['100g fresh mozzarella', '50g parmesan'],
            'aromatics': ['2 cloves garlic', '1 small onion'],
            'seasonings': ['3 tbsp olive oil', '1 tsp salt', '1/2 tsp black pepper'],
            'herbs': ['fresh basil leaves', 'dried oregano']
        },
        'instructions': [
            'Preheat oven to 475°F (245°C)',
            'Roll out pizza dough on floured surface to 12-inch circle',
            'Brush dough with olive oil and minced garlic',
            'Slice tomatoes and mozzarella into thin rounds',
            'Spread tomato slices evenly on dough',
            'Add mozzarella slices and sprinkle with salt and pepper',
            'Bake for 12-15 minutes until crust is golden and cheese bubbles',
            'Remove from oven, top with fresh basil and serve hot'
        ],
        'prep_time': 25,
        'servings': 2,
        'calories': 380,
        'cuisine': 'Italian',
        'diet_type': 'Vegetarian',
        'difficulty': 'Easy',
        'tips': 'Use fresh mozzarella for best flavor. Don\'t overload with toppings.'
    },
    {
        'id': 3,
        'name': 'Chicken Parmigiana',
        'ingredients': {
            'main': ['4 chicken breasts', '200g spaghetti', '2 cups breadcrumbs'],
            'dairy': ['150g mozzarella', '100g parmesan', '2 eggs'],
            'vegetables': ['400g tomato sauce', '1 onion', '2 cloves garlic'],
            'seasonings': ['1/2 cup flour', '1/4 cup olive oil', 'salt', 'pepper'],
            'herbs': ['fresh basil', 'dried oregano', 'parsley']
        },
        'instructions': [
            'Pound chicken breasts to 1/2 inch thickness',
            'Set up breading station: flour, beaten eggs, breadcrumbs',
            'Dredge chicken in flour, dip in eggs, coat with breadcrumbs',
            'Heat oil in large skillet, fry chicken until golden (4-5 min per side)',
            'Cook spaghetti according to package directions',
            'Layer fried chicken with tomato sauce and cheeses in baking dish',
            'Bake at 375°F for 20-25 minutes until cheese melts',
            'Serve over spaghetti with fresh basil'
        ],
        'prep_time': 45,
        'servings': 4,
        'calories': 520,
        'cuisine': 'Italian',
        'diet_type': 'Non-Veg',
        'difficulty': 'Medium',
        'tips': 'Don\'t skip pounding the chicken - it ensures even cooking.'
    }
]

def generate_all_recipes():
    """Generate 80 recipes (20 each for Italian, Chinese, American, Indian)"""
    all_recipes = []

    # Italian recipes (20)
    italian_recipes = [
        {'name': 'Spaghetti Carbonara', 'ingredients': {'main': ['400g spaghetti', '4 eggs', '150g pancetta'], 'dairy': ['100g parmesan cheese', '2 tbsp heavy cream'], 'aromatics': ['3 garlic cloves', '1 medium onion'], 'seasonings': ['Black pepper', 'Salt', 'Olive oil'], 'herbs': ['Fresh parsley', 'Fresh basil']}, 'prep_time': 30, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Margherita Pizza', 'ingredients': {'main': ['1 pizza dough', '200g mozzarella cheese', '3 large tomatoes'], 'dairy': ['Fresh mozzarella', 'Parmesan cheese'], 'seasonings': ['Olive oil', 'Salt', 'Black pepper'], 'herbs': ['Fresh basil leaves', 'Dried oregano']}, 'prep_time': 25, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken Parmigiana', 'ingredients': {'main': ['4 chicken breasts', '200g spaghetti', '2 cups breadcrumbs'], 'dairy': ['150g mozzarella', '100g parmesan', '2 eggs'], 'vegetables': ['400g tomato sauce', '1 onion', '2 garlic cloves'], 'seasonings': ['Flour', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh basil', 'Oregano', 'Parsley']}, 'prep_time': 45, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Fettuccine Alfredo', 'ingredients': {'main': ['400g fettuccine pasta', '200g butter'], 'dairy': ['300ml heavy cream', '150g parmesan cheese'], 'aromatics': ['3 garlic cloves'], 'seasonings': ['Salt', 'White pepper', 'Nutmeg'], 'herbs': ['Fresh parsley']}, 'prep_time': 20, 'calories': 480, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Lasagna Bolognese', 'ingredients': {'main': ['12 lasagna sheets', '500g ground beef', '400g ricotta cheese'], 'dairy': ['300g mozzarella', '100g parmesan', '200ml milk'], 'vegetables': ['400g tomatoes', '2 onions', '2 carrots', '2 celery stalks'], 'seasonings': ['Olive oil', 'Salt', 'Black pepper'], 'herbs': ['Fresh basil', 'Oregano', 'Thyme']}, 'prep_time': 90, 'calories': 520, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Risotto Mushroom', 'ingredients': {'main': ['300g arborio rice', '400g mixed mushrooms'], 'dairy': ['100g parmesan cheese', '50g butter'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['White wine', 'Chicken stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh thyme', 'Parsley']}, 'prep_time': 35, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Caprese Salad', 'ingredients': {'main': ['4 large tomatoes'], 'dairy': ['250g fresh mozzarella'], 'seasonings': ['Extra virgin olive oil', 'Balsamic vinegar', 'Salt', 'Black pepper'], 'herbs': ['Fresh basil leaves']}, 'prep_time': 10, 'calories': 220, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Osso Buco', 'ingredients': {'main': ['4 veal shanks', '2 tbsp flour'], 'vegetables': ['2 onions', '2 carrots', '2 celery stalks', '400g tomatoes'], 'seasonings': ['White wine', 'Beef stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh thyme', 'Bay leaves', 'Parsley']}, 'prep_time': 120, 'calories': 450, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Minestrone Soup', 'ingredients': {'main': ['200g pasta', '400g mixed beans'], 'vegetables': ['2 onions', '3 carrots', '3 celery stalks', '400g tomatoes', '200g spinach'], 'seasonings': ['Vegetable stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh basil', 'Oregano', 'Parsley']}, 'prep_time': 40, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Tiramisu', 'ingredients': {'main': ['24 ladyfinger cookies', '6 egg yolks'], 'dairy': ['500g mascarpone cheese', '200ml heavy cream'], 'seasonings': ['Strong coffee', 'Sugar', 'Cocoa powder', 'Dark rum'], 'others': ['Dark chocolate shavings']}, 'prep_time': 30, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Penne Arrabbiata', 'ingredients': {'main': ['400g penne pasta'], 'vegetables': ['400g tomatoes', '4 garlic cloves', '2 red chilies'], 'seasonings': ['Olive oil', 'Salt', 'Red pepper flakes'], 'herbs': ['Fresh parsley', 'Fresh basil']}, 'prep_time': 25, 'calories': 350, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Gnocchi with Sage Butter', 'ingredients': {'main': ['500g potato gnocchi'], 'dairy': ['100g butter', '100g parmesan cheese'], 'seasonings': ['Salt', 'Black pepper'], 'herbs': ['Fresh sage leaves']}, 'prep_time': 15, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Bruschetta', 'ingredients': {'main': ['8 bread slices'], 'vegetables': ['4 tomatoes', '2 garlic cloves'], 'seasonings': ['Extra virgin olive oil', 'Balsamic vinegar', 'Salt', 'Pepper'], 'herbs': ['Fresh basil']}, 'prep_time': 15, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Seafood Risotto', 'ingredients': {'main': ['300g arborio rice', '400g mixed seafood'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['White wine', 'Fish stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh parsley', 'Lemon zest']}, 'prep_time': 40, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Eggplant Parmigiana', 'ingredients': {'main': ['2 large eggplants'], 'dairy': ['300g mozzarella', '100g parmesan cheese'], 'vegetables': ['400g tomato sauce'], 'seasonings': ['Flour', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh basil', 'Oregano']}, 'prep_time': 60, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Prosciutto e Melone', 'ingredients': {'main': ['200g prosciutto', '1 cantaloupe melon'], 'seasonings': ['Black pepper'], 'herbs': ['Fresh mint leaves']}, 'prep_time': 10, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Pasta Puttanesca', 'ingredients': {'main': ['400g spaghetti'], 'vegetables': ['400g tomatoes', '3 garlic cloves'], 'seasonings': ['Olive oil', 'Capers', 'Black olives', 'Anchovies', 'Red pepper flakes'], 'herbs': ['Fresh parsley']}, 'prep_time': 25, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Polenta with Mushrooms', 'ingredients': {'main': ['200g polenta', '400g mushrooms'], 'dairy': ['100g parmesan cheese', '50g butter'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['Vegetable stock', 'Olive oil', 'Salt', 'Pepper'], 'herbs': ['Fresh thyme', 'Parsley']}, 'prep_time': 35, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Vitello Tonnato', 'ingredients': {'main': ['800g veal roast', '200g tuna'], 'dairy': ['200ml mayonnaise'], 'seasonings': ['White wine', 'Capers', 'Lemon juice', 'Olive oil'], 'herbs': ['Fresh parsley']}, 'prep_time': 90, 'calories': 380, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Focaccia Bread', 'ingredients': {'main': ['500g bread flour', '7g active yeast'], 'vegetables': ['2 onions', 'Cherry tomatoes'], 'seasonings': ['Olive oil', 'Salt', 'Sugar'], 'herbs': ['Fresh rosemary', 'Oregano']}, 'prep_time': 120, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Vegan'}
    ]

    # Chinese recipes (20)
    chinese_recipes = [
        {'name': 'Kung Pao Chicken', 'ingredients': {'main': ['500g chicken breast', '100g peanuts'], 'vegetables': ['2 bell peppers', '1 onion', '3 garlic cloves', '2 dried chilies'], 'seasonings': ['Soy sauce', 'Rice vinegar', 'Cornstarch', 'Sesame oil'], 'aromatics': ['Fresh ginger', 'Green onions']}, 'prep_time': 25, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Sweet and Sour Pork', 'ingredients': {'main': ['500g pork shoulder'], 'vegetables': ['1 bell pepper', '1 onion', '200g pineapple'], 'seasonings': ['Rice vinegar', 'Ketchup', 'Sugar', 'Soy sauce', 'Cornstarch'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 35, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Mapo Tofu', 'ingredients': {'main': ['400g silken tofu', '200g ground pork'], 'vegetables': ['3 garlic cloves', '2 green onions'], 'seasonings': ['Doubanjiang', 'Soy sauce', 'Cornstarch', 'Sichuan peppercorns'], 'aromatics': ['Fresh ginger']}, 'prep_time': 20, 'calories': 350, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Fried Rice', 'ingredients': {'main': ['3 cups cooked rice', '3 eggs'], 'vegetables': ['1 cup mixed vegetables', '3 green onions'], 'seasonings': ['Soy sauce', 'Sesame oil', 'Vegetable oil'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 15, 'calories': 320, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Hot Pot', 'ingredients': {'main': ['300g beef slices', '200g tofu', '200g mushrooms'], 'vegetables': ['Napa cabbage', 'Bean sprouts'], 'seasonings': ['Hot pot base', 'Sesame oil', 'Soy sauce'], 'aromatics': ['Garlic', 'Scallions']}, 'prep_time': 30, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Dumplings', 'ingredients': {'main': ['30 dumpling wrappers', '300g ground pork'], 'vegetables': ['2 cups cabbage', '2 green onions'], 'seasonings': ['Soy sauce', 'Sesame oil', 'Rice wine'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 45, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'General Tso Chicken', 'ingredients': {'main': ['500g chicken thighs'], 'vegetables': ['2 garlic cloves', '1 inch ginger'], 'seasonings': ['Soy sauce', 'Rice vinegar', 'Sugar', 'Cornstarch', 'Red pepper flakes'], 'aromatics': ['Green onions']}, 'prep_time': 30, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Chow Mein', 'ingredients': {'main': ['400g fresh noodles', '200g chicken'], 'vegetables': ['1 cup bean sprouts', '1 bell pepper', '2 carrots'], 'seasonings': ['Soy sauce', 'Oyster sauce', 'Sesame oil'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 20, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Wonton Soup', 'ingredients': {'main': ['20 wonton wrappers', '200g ground pork'], 'vegetables': ['Bok choy', '2 green onions'], 'seasonings': ['Chicken broth', 'Soy sauce', 'Sesame oil'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 40, 'calories': 250, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Orange Chicken', 'ingredients': {'main': ['500g chicken breast'], 'vegetables': ['1 orange zest'], 'seasonings': ['Orange juice', 'Soy sauce', 'Rice vinegar', 'Sugar', 'Cornstarch'], 'aromatics': ['Fresh ginger', 'Garlic']}, 'prep_time': 25, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Ma Po Eggplant', 'ingredients': {'main': ['2 large eggplants'], 'vegetables': ['3 garlic cloves', '2 green onions'], 'seasonings': ['Doubanjiang', 'Soy sauce', 'Sugar', 'Cornstarch'], 'aromatics': ['Fresh ginger']}, 'prep_time': 25, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Beef and Broccoli', 'ingredients': {'main': ['400g beef sirloin', '300g broccoli'], 'vegetables': ['2 garlic cloves'], 'seasonings': ['Soy sauce', 'Oyster sauce', 'Cornstarch', 'Sesame oil'], 'aromatics': ['Fresh ginger']}, 'prep_time': 20, 'calories': 320, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Spring Rolls', 'ingredients': {'main': ['12 spring roll wrappers'], 'vegetables': ['2 cups cabbage', '1 carrot', '100g bean sprouts'], 'seasonings': ['Soy sauce', 'Sesame oil', 'Vegetable oil'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 30, 'calories': 220, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Char Siu Pork', 'ingredients': {'main': ['800g pork shoulder'], 'seasonings': ['Hoisin sauce', 'Soy sauce', 'Honey', 'Rice wine', 'Five spice powder'], 'aromatics': ['Garlic', 'Ginger']}, 'prep_time': 180, 'calories': 380, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Congee', 'ingredients': {'main': ['1 cup rice', '200g chicken'], 'vegetables': ['2 green onions'], 'seasonings': ['Chicken broth', 'Soy sauce', 'Sesame oil', 'Salt'], 'aromatics': ['Fresh ginger']}, 'prep_time': 60, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Dan Dan Noodles', 'ingredients': {'main': ['400g fresh noodles', '200g ground pork'], 'vegetables': ['2 green onions'], 'seasonings': ['Sesame paste', 'Chili oil', 'Soy sauce', 'Black vinegar'], 'aromatics': ['Garlic', 'Sichuan peppercorns']}, 'prep_time': 25, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Salt and Pepper Shrimp', 'ingredients': {'main': ['500g large shrimp'], 'vegetables': ['2 jalapeños', '3 garlic cloves'], 'seasonings': ['Salt', 'White pepper', 'Cornstarch', 'Vegetable oil'], 'aromatics': ['Green onions']}, 'prep_time': 20, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Twice Cooked Pork', 'ingredients': {'main': ['400g pork belly'], 'vegetables': ['2 bell peppers', '1 onion', '3 garlic cloves'], 'seasonings': ['Doubanjiang', 'Soy sauce', 'Sugar'], 'aromatics': ['Fresh ginger']}, 'prep_time': 35, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Egg Drop Soup', 'ingredients': {'main': ['4 eggs'], 'vegetables': ['2 green onions'], 'seasonings': ['Chicken broth', 'Cornstarch', 'Sesame oil', 'White pepper'], 'aromatics': ['Fresh ginger']}, 'prep_time': 15, 'calories': 120, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'}
    ]

    # Add Italian recipes
    for i, recipe_data in enumerate(italian_recipes):
        recipe = create_recipe(i + 1, recipe_data, 'Italian')
        all_recipes.append(recipe)

    # American recipes (20)
    american_recipes = [
        {'name': 'Classic Burger', 'ingredients': {'main': ['500g ground beef', '4 burger buns'], 'vegetables': ['2 tomatoes', '1 onion', 'Lettuce leaves'], 'dairy': ['4 cheese slices'], 'seasonings': ['Salt', 'Black pepper', 'Ketchup', 'Mustard'], 'others': ['Pickles']}, 'prep_time': 20, 'calories': 550, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'BBQ Ribs', 'ingredients': {'main': ['1kg pork ribs'], 'seasonings': ['BBQ sauce', 'Brown sugar', 'Paprika', 'Garlic powder', 'Onion powder', 'Salt', 'Black pepper'], 'aromatics': ['Apple cider vinegar']}, 'prep_time': 180, 'calories': 480, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Mac and Cheese', 'ingredients': {'main': ['400g macaroni pasta'], 'dairy': ['300g cheddar cheese', '200ml milk', '50g butter'], 'seasonings': ['Flour', 'Salt', 'Black pepper', 'Mustard powder'], 'others': ['Breadcrumbs']}, 'prep_time': 30, 'calories': 420, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Fried Chicken', 'ingredients': {'main': ['1 whole chicken', '2 cups flour'], 'dairy': ['2 cups buttermilk'], 'seasonings': ['Salt', 'Black pepper', 'Paprika', 'Garlic powder', 'Cayenne pepper'], 'others': ['Vegetable oil']}, 'prep_time': 45, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Caesar Salad', 'ingredients': {'main': ['2 romaine lettuce heads'], 'dairy': ['100g parmesan cheese', '1 egg yolk'], 'seasonings': ['Olive oil', 'Lemon juice', 'Worcestershire sauce', 'Garlic'], 'others': ['Croutons', 'Anchovies']}, 'prep_time': 15, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Clam Chowder', 'ingredients': {'main': ['500g clams', '4 potatoes'], 'dairy': ['200ml heavy cream', '50g butter'], 'vegetables': ['2 onions', '2 celery stalks'], 'seasonings': ['Flour', 'Bay leaves', 'Thyme', 'Salt', 'Pepper'], 'others': ['Bacon']}, 'prep_time': 40, 'calories': 350, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Buffalo Wings', 'ingredients': {'main': ['1kg chicken wings'], 'seasonings': ['Hot sauce', 'Butter', 'Garlic powder', 'Salt'], 'others': ['Blue cheese dressing', 'Celery sticks']}, 'prep_time': 35, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Meatloaf', 'ingredients': {'main': ['800g ground beef', '2 eggs'], 'vegetables': ['1 onion', '2 garlic cloves'], 'seasonings': ['Breadcrumbs', 'Ketchup', 'Worcestershire sauce', 'Salt', 'Pepper'], 'herbs': ['Fresh parsley']}, 'prep_time': 75, 'calories': 420, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Apple Pie', 'ingredients': {'main': ['6 apples', '2 pie crusts'], 'seasonings': ['Sugar', 'Cinnamon', 'Nutmeg', 'Flour', 'Butter'], 'others': ['Lemon juice']}, 'prep_time': 90, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Pancakes', 'ingredients': {'main': ['2 cups flour', '2 eggs'], 'dairy': ['1.5 cups milk', '50g butter'], 'seasonings': ['Sugar', 'Baking powder', 'Salt'], 'others': ['Maple syrup']}, 'prep_time': 20, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Pulled Pork', 'ingredients': {'main': ['1.5kg pork shoulder'], 'seasonings': ['BBQ sauce', 'Brown sugar', 'Paprika', 'Chili powder', 'Cumin', 'Salt'], 'aromatics': ['Apple cider vinegar', 'Onion powder']}, 'prep_time': 480, 'calories': 420, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Cornbread', 'ingredients': {'main': ['1 cup cornmeal', '1 cup flour'], 'dairy': ['1 cup milk', '100g butter', '2 eggs'], 'seasonings': ['Sugar', 'Baking powder', 'Salt']}, 'prep_time': 30, 'calories': 220, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Jambalaya', 'ingredients': {'main': ['2 cups rice', '300g shrimp', '200g sausage'], 'vegetables': ['2 onions', '2 bell peppers', '3 celery stalks', '400g tomatoes'], 'seasonings': ['Chicken stock', 'Cajun seasoning', 'Bay leaves'], 'aromatics': ['Garlic', 'Green onions']}, 'prep_time': 45, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Coleslaw', 'ingredients': {'main': ['1 cabbage head', '2 carrots'], 'dairy': ['Mayonnaise'], 'seasonings': ['Apple cider vinegar', 'Sugar', 'Salt', 'Celery seed']}, 'prep_time': 15, 'calories': 120, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Biscuits and Gravy', 'ingredients': {'main': ['2 cups flour', '300g sausage'], 'dairy': ['Buttermilk', 'Butter', 'Milk'], 'seasonings': ['Baking powder', 'Salt', 'Black pepper']}, 'prep_time': 30, 'calories': 450, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Chili Con Carne', 'ingredients': {'main': ['500g ground beef', '400g kidney beans'], 'vegetables': ['2 onions', '3 garlic cloves', '400g tomatoes', '2 bell peppers'], 'seasonings': ['Chili powder', 'Cumin', 'Paprika', 'Salt', 'Pepper'], 'aromatics': ['Bay leaves']}, 'prep_time': 60, 'calories': 320, 'difficulty': 'Easy', 'diet_type': 'Non-Veg'},
        {'name': 'Grilled Cheese', 'ingredients': {'main': ['8 bread slices'], 'dairy': ['200g cheddar cheese', 'Butter']}, 'prep_time': 10, 'calories': 380, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken and Waffles', 'ingredients': {'main': ['4 chicken breasts', '2 cups flour'], 'dairy': ['2 eggs', 'Milk', 'Butter'], 'seasonings': ['Baking powder', 'Salt', 'Sugar', 'Vanilla'], 'others': ['Maple syrup']}, 'prep_time': 40, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Potato Salad', 'ingredients': {'main': ['1kg potatoes', '4 eggs'], 'dairy': ['Mayonnaise'], 'vegetables': ['2 onions', '3 celery stalks'], 'seasonings': ['Mustard', 'Salt', 'Pepper', 'Paprika'], 'herbs': ['Fresh dill']}, 'prep_time': 30, 'calories': 250, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Meatball Subs', 'ingredients': {'main': ['500g ground beef', '4 sub rolls'], 'dairy': ['200g mozzarella cheese'], 'vegetables': ['400g marinara sauce', '1 onion'], 'seasonings': ['Breadcrumbs', 'Italian seasoning', 'Salt', 'Pepper'], 'herbs': ['Fresh basil']}, 'prep_time': 35, 'calories': 480, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'}
    ]

    # Indian recipes (20)
    indian_recipes = [
        {'name': 'Butter Chicken', 'ingredients': {'main': ['600g chicken breast'], 'dairy': ['200ml heavy cream', '100g butter', '200g yogurt'], 'vegetables': ['400g tomatoes', '2 onions', '4 garlic cloves'], 'spices': ['Garam masala', 'Turmeric', 'Cumin', 'Coriander', 'Red chili powder'], 'aromatics': ['Fresh ginger', 'Green chilies']}, 'prep_time': 45, 'calories': 520, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Dal Tadka', 'ingredients': {'main': ['200g yellow lentils'], 'vegetables': ['2 onions', '3 tomatoes', '4 garlic cloves'], 'spices': ['Cumin seeds', 'Turmeric', 'Coriander powder', 'Red chili powder'], 'aromatics': ['Fresh ginger', 'Green chilies'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 30, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Palak Paneer', 'ingredients': {'main': ['400g paneer', '500g spinach'], 'dairy': ['100ml cream'], 'vegetables': ['2 onions', '3 tomatoes', '4 garlic cloves'], 'spices': ['Garam masala', 'Cumin', 'Coriander'], 'aromatics': ['Fresh ginger', 'Green chilies']}, 'prep_time': 35, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken Biryani', 'ingredients': {'main': ['500g basmati rice', '800g chicken'], 'dairy': ['200g yogurt'], 'vegetables': ['3 onions'], 'spices': ['Biryani masala', 'Saffron', 'Bay leaves', 'Cardamom', 'Cinnamon'], 'aromatics': ['Ginger-garlic paste', 'Mint', 'Coriander'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 90, 'calories': 480, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Masala Dosa', 'ingredients': {'main': ['2 cups rice', '1 cup urad dal', '4 potatoes'], 'vegetables': ['2 onions', '2 green chilies'], 'spices': ['Mustard seeds', 'Turmeric', 'Curry leaves'], 'aromatics': ['Fresh ginger'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 30, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Rajma Curry', 'ingredients': {'main': ['400g kidney beans'], 'vegetables': ['2 onions', '3 tomatoes', '4 garlic cloves'], 'spices': ['Cumin powder', 'Coriander powder', 'Garam masala', 'Red chili powder'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 45, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Tandoori Chicken', 'ingredients': {'main': ['1 whole chicken'], 'dairy': ['200g yogurt'], 'spices': ['Tandoori masala', 'Red chili powder', 'Turmeric', 'Garam masala'], 'aromatics': ['Ginger-garlic paste', 'Lemon juice'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 60, 'calories': 380, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Chole Bhature', 'ingredients': {'main': ['400g chickpeas', '2 cups flour'], 'dairy': ['Yogurt'], 'vegetables': ['2 onions', '3 tomatoes'], 'spices': ['Chole masala', 'Cumin', 'Coriander', 'Amchur'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 60, 'calories': 420, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Fish Curry', 'ingredients': {'main': ['600g fish fillets'], 'vegetables': ['2 onions', '3 tomatoes', '1 coconut'], 'spices': ['Turmeric', 'Red chili powder', 'Coriander powder', 'Mustard seeds'], 'aromatics': ['Fresh ginger', 'Curry leaves'], 'seasonings': ['Coconut oil', 'Salt']}, 'prep_time': 35, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Aloo Gobi', 'ingredients': {'main': ['4 potatoes', '1 cauliflower'], 'vegetables': ['2 onions', '2 tomatoes'], 'spices': ['Turmeric', 'Cumin seeds', 'Coriander powder', 'Garam masala'], 'aromatics': ['Fresh ginger', 'Green chilies'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 30, 'calories': 180, 'difficulty': 'Easy', 'diet_type': 'Vegan'},
        {'name': 'Samosas', 'ingredients': {'main': ['2 cups flour', '4 potatoes'], 'vegetables': ['1 onion', '2 green chilies'], 'spices': ['Cumin seeds', 'Coriander seeds', 'Garam masala', 'Turmeric'], 'aromatics': ['Fresh ginger'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 45, 'calories': 220, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Paneer Tikka', 'ingredients': {'main': ['400g paneer'], 'dairy': ['200g yogurt'], 'vegetables': ['2 bell peppers', '2 onions'], 'spices': ['Tandoori masala', 'Red chili powder', 'Turmeric'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 30, 'calories': 280, 'difficulty': 'Easy', 'diet_type': 'Vegetarian'},
        {'name': 'Lamb Curry', 'ingredients': {'main': ['800g lamb'], 'dairy': ['100g yogurt'], 'vegetables': ['3 onions', '4 tomatoes'], 'spices': ['Garam masala', 'Red chili powder', 'Turmeric', 'Coriander powder'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 90, 'calories': 450, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Idli Sambar', 'ingredients': {'main': ['2 cups rice', '1 cup urad dal', '200g toor dal'], 'vegetables': ['2 tomatoes', '1 onion', 'Drumsticks', 'Okra'], 'spices': ['Sambar powder', 'Turmeric', 'Mustard seeds', 'Curry leaves'], 'aromatics': ['Tamarind'], 'seasonings': ['Oil', 'Salt']}, 'prep_time': 40, 'calories': 250, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Rogan Josh', 'ingredients': {'main': ['800g lamb'], 'dairy': ['200g yogurt'], 'vegetables': ['3 onions'], 'spices': ['Kashmiri red chili', 'Fennel powder', 'Ginger powder', 'Garam masala'], 'aromatics': ['Garlic'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 120, 'calories': 420, 'difficulty': 'Hard', 'diet_type': 'Non-Veg'},
        {'name': 'Pav Bhaji', 'ingredients': {'main': ['8 pav buns'], 'vegetables': ['4 potatoes', '2 onions', '3 tomatoes', '1 bell pepper', '200g cauliflower'], 'spices': ['Pav bhaji masala', 'Red chili powder', 'Turmeric'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Butter', 'Salt']}, 'prep_time': 40, 'calories': 350, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Chicken Tikka Masala', 'ingredients': {'main': ['600g chicken breast'], 'dairy': ['200ml heavy cream', '200g yogurt'], 'vegetables': ['2 onions', '400g tomatoes'], 'spices': ['Garam masala', 'Paprika', 'Cumin', 'Coriander'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 50, 'calories': 480, 'difficulty': 'Medium', 'diet_type': 'Non-Veg'},
        {'name': 'Vegetable Biryani', 'ingredients': {'main': ['400g basmati rice'], 'vegetables': ['2 potatoes', '200g cauliflower', '200g carrots', '200g beans', '2 onions'], 'spices': ['Biryani masala', 'Saffron', 'Bay leaves'], 'aromatics': ['Mint', 'Coriander'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 60, 'calories': 320, 'difficulty': 'Medium', 'diet_type': 'Vegan'},
        {'name': 'Gulab Jamun', 'ingredients': {'main': ['200g milk powder', '50g flour'], 'dairy': ['Milk', 'Ghee'], 'seasonings': ['Sugar', 'Cardamom', 'Rose water'], 'others': ['Oil for frying']}, 'prep_time': 45, 'calories': 280, 'difficulty': 'Medium', 'diet_type': 'Vegetarian'},
        {'name': 'Malai Kofta', 'ingredients': {'main': ['400g paneer', '4 potatoes'], 'dairy': ['200ml heavy cream'], 'vegetables': ['2 onions', '3 tomatoes'], 'spices': ['Garam masala', 'Red chili powder', 'Turmeric'], 'aromatics': ['Ginger-garlic paste'], 'seasonings': ['Ghee', 'Salt']}, 'prep_time': 50, 'calories': 380, 'difficulty': 'Hard', 'diet_type': 'Vegetarian'}
    ]

    # Add Chinese recipes
    for i, recipe_data in enumerate(chinese_recipes):
        recipe = create_recipe(i + 21, recipe_data, 'Chinese')
        all_recipes.append(recipe)

    # Add American recipes
    for i, recipe_data in enumerate(american_recipes):
        recipe = create_recipe(i + 41, recipe_data, 'American')
        all_recipes.append(recipe)

    # Add Indian recipes
    for i, recipe_data in enumerate(indian_recipes):
        recipe = create_recipe(i + 61, recipe_data, 'Indian')
        all_recipes.append(recipe)

    return all_recipes

def create_recipe(recipe_id, recipe_data, cuisine):
    """Create a properly formatted recipe"""
    return {
        'id': recipe_id,
        'name': recipe_data['name'],
        'ingredients': recipe_data['ingredients'],
        'instructions': generate_instructions(recipe_data['name']),
        'prep_time': recipe_data['prep_time'],
        'servings': 2 + (recipe_id % 4),
        'calories': recipe_data['calories'],
        'cuisine': cuisine,
        'diet_type': recipe_data['diet_type'],
        'difficulty': recipe_data['difficulty'],
        'tips': f"Pro tip: Use fresh, high-quality ingredients for the best {recipe_data['name']}."
    }

def generate_instructions(recipe_name):
    """Generate basic instructions for recipes"""
    instructions_map = {
        'Spaghetti Carbonara': [
            'Boil salted water and cook pasta until al dente',
            'Cook pancetta until crispy in large skillet',
            'Whisk eggs with cheese and seasonings',
            'Combine hot pasta with pancetta off heat',
            'Add egg mixture quickly while tossing',
            'Serve immediately with extra cheese'
        ],
        'Margherita Pizza': [
            'Preheat oven to 475°F',
            'Roll out pizza dough',
            'Add tomato sauce and seasonings',
            'Top with mozzarella and basil',
            'Bake for 12-15 minutes until golden',
            'Serve hot with fresh basil'
        ],
        'Lasagna Bolognese': [
            'Prepare meat sauce with ground beef',
            'Cook lasagna sheets until al dente',
            'Layer pasta, meat sauce, and cheese',
            'Repeat layers ending with cheese',
            'Bake covered for 45 minutes',
            'Rest 10 minutes before serving'
        ]
    }

    base_name = recipe_name.split()[0] + ' ' + recipe_name.split()[1] if len(recipe_name.split()) > 1 else recipe_name
    return instructions_map.get(base_name, [
        'Prepare all ingredients as specified',
        'Follow traditional cooking methods',
        'Cook until properly done',
        'Season to taste',
        'Serve hot and enjoy'
    ])

# Generate all recipes
recipes = generate_all_recipes()

meal_plans = []

# Common ingredients for image recognition simulation
COMMON_INGREDIENTS = [
    'tomatoes', 'onion', 'garlic', 'ginger', 'chicken', 'beef', 'pork', 'fish', 'eggs', 'cheese',
    'rice', 'pasta', 'bread', 'potatoes', 'carrots', 'bell peppers', 'spinach', 'broccoli',
    'mushrooms', 'avocado', 'lemon', 'lime', 'basil', 'cilantro', 'parsley', 'soy sauce',
    'olive oil', 'butter', 'cream', 'milk', 'flour', 'sugar', 'salt', 'black pepper'
]

def calculate_recipe_match(user_ingredients, recipe_ingredients):
    """Calculate match percentage and missing ingredients for a recipe"""
    user_ingredients_lower = [ing.lower().strip() for ing in user_ingredients]

    # Handle both old format (list) and new format (dict with categories)
    if isinstance(recipe_ingredients, dict):
        # Flatten categorized ingredients
        all_recipe_ingredients = []
        for category, items in recipe_ingredients.items():
            all_recipe_ingredients.extend(items)
        recipe_ingredients_lower = [ing.lower().strip() for ing in all_recipe_ingredients]
    else:
        recipe_ingredients_lower = [ing.lower().strip() for ing in recipe_ingredients]

    matched_ingredients = []
    missing_ingredients = []

    for recipe_ing in recipe_ingredients_lower:
        found = False
        for user_ing in user_ingredients_lower:
            # More flexible matching - check if any word matches
            recipe_words = recipe_ing.split()
            user_words = user_ing.split()

            for recipe_word in recipe_words:
                for user_word in user_words:
                    if (len(recipe_word) > 3 and len(user_word) > 3 and
                        (recipe_word in user_word or user_word in recipe_word)):
                        matched_ingredients.append(recipe_ing)
                        found = True
                        break
                if found:
                    break
            if found:
                break

        if not found:
            missing_ingredients.append(recipe_ing)

    match_percentage = (len(matched_ingredients) / len(recipe_ingredients_lower)) * 100 if recipe_ingredients_lower else 0

    return {
        'match_percentage': round(match_percentage, 1),
        'matched_ingredients': matched_ingredients,
        'missing_ingredients': missing_ingredients,
        'total_ingredients': len(recipe_ingredients_lower)
    }

def simulate_ingredient_detection(image_path):
    """Simulate ingredient detection from image (placeholder for actual AI/ML model)"""
    # This is a simulation - in a real app, you'd use computer vision/ML models
    import random
    detected_ingredients = random.sample(COMMON_INGREDIENTS, random.randint(3, 8))
    return detected_ingredients

# Helper functions
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def get_current_user():
    return session.get('user_id')

def is_logged_in():
    return 'user_id' in session

# Authentication routes
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        full_name = request.form['full_name']

        # Check if user already exists
        if any(user['email'] == email for user in users.values()):
            flash('Email already registered', 'error')
            return render_template('auth.html', mode='register')

        # Create new user
        user_id = str(uuid.uuid4())
        users[user_id] = {
            'id': user_id,
            'username': username,
            'email': email,
            'password': hash_password(password),
            'full_name': full_name,
            'created_at': datetime.now(),
            'avatar': None
        }
        user_favorites[user_id] = []
        user_profiles[user_id] = {
            'dietary_preferences': [],
            'favorite_cuisines': [],
            'cooking_skill': 'Beginner',
            'allergies': []
        }

        session['user_id'] = user_id
        flash('Registration successful! Welcome to MealMind!', 'success')
        return redirect(url_for('index'))

    return render_template('auth.html', mode='register')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']

        # Find user by email
        user = None
        for u in users.values():
            if u['email'] == email and u['password'] == hash_password(password):
                user = u
                break

        if user:
            session['user_id'] = user['id']
            flash(f'Welcome back, {user["full_name"]}!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Invalid email or password', 'error')

    return render_template('auth.html', mode='login')

@app.route('/logout')
def logout():
    session.pop('user_id', None)
    flash('You have been logged out', 'info')
    return redirect(url_for('index'))

@app.route('/profile')
def profile():
    if not is_logged_in():
        flash('Please log in to view your profile', 'error')
        return redirect(url_for('login'))

    user_id = get_current_user()
    user = users[user_id]
    profile_data = user_profiles[user_id]
    favorites = user_favorites[user_id]
    favorite_recipes = [recipe for recipe in recipes if recipe['id'] in favorites]

    return render_template('profile.html', user=user, profile=profile_data, favorite_recipes=favorite_recipes)

@app.route('/update-profile', methods=['POST'])
def update_profile():
    if not is_logged_in():
        return jsonify({'error': 'Not logged in'}), 401

    user_id = get_current_user()
    data = request.get_json()

    if 'user_info' in data:
        users[user_id].update(data['user_info'])

    if 'profile_info' in data:
        user_profiles[user_id].update(data['profile_info'])

    return jsonify({'success': True})

@app.route('/')
def index():
    """Main landing page"""
    user = users.get(get_current_user()) if is_logged_in() else None
    return render_template('index.html', user=user)

@app.route('/smart-recipes')
def smart_recipes():
    """Smart recipes page with ingredient finder"""
    cuisines = ['Italian', 'Chinese', 'American', 'Indian']
    diet_types = list(set([recipe['diet_type'] for recipe in recipes]))
    user = users.get(get_current_user()) if is_logged_in() else None
    return render_template('smart_recipes.html', cuisines=cuisines, diet_types=diet_types, user=user, user_favorites=user_favorites)

@app.route('/browse')
def browse_recipes():
    """Browse all recipes page"""
    cuisines = ['Italian', 'Chinese', 'American', 'Indian']
    diet_types = list(set([recipe['diet_type'] for recipe in recipes]))
    user = users.get(get_current_user()) if is_logged_in() else None
    return render_template('browse.html', recipes=recipes, cuisines=cuisines, diet_types=diet_types, user=user, user_favorites=user_favorites)

@app.route('/favorites')
def favorites():
    """User favorites page"""
    if not is_logged_in():
        flash('Please log in to view your favorites', 'error')
        return redirect(url_for('login'))

    user_id = get_current_user()
    user = users[user_id]
    favorites_list = user_favorites.get(user_id, [])
    favorite_recipes = [recipe for recipe in recipes if recipe['id'] in favorites_list]

    return render_template('favorites.html', favorite_recipes=favorite_recipes, user=user)

@app.route('/recipe/<int:recipe_id>')
def recipe_detail(recipe_id):
    recipe = next((r for r in recipes if r['id'] == recipe_id), None)
    if recipe:
        return render_template('recipe.html', recipe=recipe)
    flash('Recipe not found', 'error')
    return redirect(url_for('index'))

@app.route('/meal-plan')
def meal_plan():
    return render_template('meal_plan.html', meal_plans=meal_plans, recipes=recipes)

@app.route('/add-to-meal-plan', methods=['POST'])
def add_to_meal_plan():
    try:
        # Handle both form data and JSON data
        if request.is_json:
            data = request.get_json()
            recipe_id = int(data['recipe_id'])
            date = data['date']
            meal_type = data['meal_type']
        else:
            recipe_id = int(request.form['recipe_id'])
            date = request.form['date']
            meal_type = request.form['meal_type']

        recipe = next((r for r in recipes if r['id'] == recipe_id), None)
        if recipe:
            # Check if meal already exists for this date and meal type
            existing_meal = next((m for m in meal_plans if m['date'] == date and m['meal_type'] == meal_type), None)
            if existing_meal:
                # Update existing meal instead of creating duplicate
                existing_meal['recipe'] = recipe
                message = f'{recipe["name"]} updated for your {meal_type} plan on {date}!'
                flash(message, 'success')
                if request.is_json:
                    return jsonify({'success': True, 'message': message})
            else:
                meal_plans.append({
                    'recipe': recipe,
                    'date': date,
                    'meal_type': meal_type
                })
                message = f'{recipe["name"]} added to your {meal_type} plan for {date}!'
                flash(message, 'success')
                if request.is_json:
                    return jsonify({'success': True, 'message': message})
        else:
            error_msg = 'Recipe not found'
            flash(error_msg, 'error')
            if request.is_json:
                return jsonify({'success': False, 'error': error_msg}), 404
    except (ValueError, KeyError) as e:
        error_msg = 'Invalid data provided'
        flash(error_msg, 'error')
        if request.is_json:
            return jsonify({'success': False, 'error': error_msg}), 400

    return redirect(url_for('meal_plan'))

@app.route('/api/recipes')
def api_recipes():
    return jsonify(recipes)

@app.route('/find-recipes', methods=['GET', 'POST'])
def find_recipes():
    if request.method == 'POST':
        user_ingredients = []

        # Handle text input
        if 'ingredients_text' in request.form and request.form['ingredients_text']:
            text_ingredients = request.form['ingredients_text'].split(',')
            user_ingredients.extend([ing.strip() for ing in text_ingredients if ing.strip()])

        # Handle image upload
        if 'ingredients_image' in request.files:
            file = request.files['ingredients_image']
            if file and file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)

                # Simulate ingredient detection from image
                detected_ingredients = simulate_ingredient_detection(filepath)
                user_ingredients.extend(detected_ingredients)

                flash(f'Detected ingredients from image: {", ".join(detected_ingredients)}', 'info')

        if not user_ingredients:
            flash('Please provide ingredients either by text or image upload', 'error')
            return redirect(url_for('index'))

        # Get filters
        cuisine_filter = request.form.get('cuisine', '')
        diet_filter = request.form.get('diet_type', '')
        max_time = request.form.get('max_time', '')

        # Find matching recipes
        matching_recipes = []
        for recipe in recipes:
            # Apply filters
            if cuisine_filter and recipe['cuisine'] != cuisine_filter:
                continue
            if diet_filter and recipe['diet_type'] != diet_filter:
                continue
            if max_time and recipe['prep_time'] > int(max_time):
                continue

            # Calculate match
            match_data = calculate_recipe_match(user_ingredients, recipe['ingredients'])
            if match_data['match_percentage'] > 0:  # Only include recipes with some match
                recipe_with_match = recipe.copy()
                recipe_with_match.update(match_data)
                matching_recipes.append(recipe_with_match)

        # Sort by match percentage
        matching_recipes.sort(key=lambda x: x['match_percentage'], reverse=True)

        return render_template('recipe_results.html',
                             recipes=matching_recipes,
                             user_ingredients=user_ingredients,
                             total_found=len(matching_recipes))

    return redirect(url_for('index'))

@app.route('/search-recipes')
def search_recipes():
    query = request.args.get('q', '').lower()
    cuisine = request.args.get('cuisine', '')
    diet_type = request.args.get('diet_type', '')
    max_time = request.args.get('max_time', '')

    filtered_recipes = []
    for recipe in recipes:
        # Apply filters
        if cuisine and recipe['cuisine'] != cuisine:
            continue
        if diet_type and recipe['diet_type'] != diet_type:
            continue
        if max_time and recipe['prep_time'] > int(max_time):
            continue

        # Apply search query
        if query:
            # Handle both old and new ingredient formats
            ingredients_match = False
            if isinstance(recipe['ingredients'], dict):
                # New categorized format
                for category, items in recipe['ingredients'].items():
                    if any(query in ingredient.lower() for ingredient in items):
                        ingredients_match = True
                        break
            else:
                # Old list format
                ingredients_match = any(query in ingredient.lower() for ingredient in recipe['ingredients'])

            # Check instructions (handle both string and list formats)
            instructions_match = False
            if isinstance(recipe['instructions'], list):
                instructions_match = any(query in instruction.lower() for instruction in recipe['instructions'])
            else:
                instructions_match = query in recipe['instructions'].lower()

            if (query in recipe['name'].lower() or
                ingredients_match or
                instructions_match or
                query in recipe['cuisine'].lower()):
                filtered_recipes.append(recipe)
        else:
            filtered_recipes.append(recipe)

    return jsonify(filtered_recipes)

@app.route('/nutrition-info/<int:recipe_id>')
def nutrition_info(recipe_id):
    recipe = next((r for r in recipes if r['id'] == recipe_id), None)
    if recipe:
        # Enhanced nutrition calculation with more realistic values
        base_calories = recipe.get('calories', 0)
        nutrition = {
            'calories': base_calories,
            'protein': base_calories * 0.25,  # 25% protein
            'carbs': base_calories * 0.45,    # 45% carbs
            'fat': base_calories * 0.30       # 30% fat
        }
        return jsonify(nutrition)
    return jsonify({'error': 'Recipe not found'}), 404

@app.route('/weekly-meal-plan')
def weekly_meal_plan():
    # Generate a week's worth of dates starting from today
    today = datetime.now().date()
    week_dates = [(today + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]

    # Organize meal plans by date
    organized_plans = {}
    for date in week_dates:
        organized_plans[date] = {
            'breakfast': [],
            'lunch': [],
            'dinner': []
        }

    # Add existing meal plans to the organized structure
    for plan in meal_plans:
        date = plan['date']
        meal_type = plan['meal_type']
        if date in organized_plans:
            organized_plans[date][meal_type].append(plan['recipe'])

    return jsonify(organized_plans)

@app.route('/api/meal-plans')
def api_meal_plans():
    """API endpoint to get all meal plans"""
    return jsonify({
        'meal_plans': meal_plans,
        'total_count': len(meal_plans)
    })

@app.route('/remove-meal-plan', methods=['POST'])
def remove_meal_plan():
    try:
        # Handle both form data and JSON data
        if request.is_json:
            data = request.get_json()
            date = data['date']
            meal_type = data['meal_type']
        else:
            date = request.form['date']
            meal_type = request.form['meal_type']

        # Find and remove the meal plan
        initial_count = len(meal_plans)
        meal_plans[:] = [m for m in meal_plans if not (m['date'] == date and m['meal_type'] == meal_type)]

        if len(meal_plans) < initial_count:
            message = f'{meal_type.title()} removed from {date}'
            flash(message, 'success')
            if request.is_json:
                return jsonify({'success': True, 'message': message})
        else:
            error_msg = 'Meal plan not found'
            flash(error_msg, 'warning')
            if request.is_json:
                return jsonify({'success': False, 'error': error_msg}), 404
    except KeyError:
        error_msg = 'Invalid data provided'
        flash(error_msg, 'error')
        if request.is_json:
            return jsonify({'success': False, 'error': error_msg}), 400

    return redirect(url_for('meal_plan'))

# Favorites functionality
@app.route('/toggle-favorite', methods=['POST'])
def toggle_favorite():
    if not is_logged_in():
        return jsonify({'error': 'Please log in to save favorites'}), 401

    user_id = get_current_user()
    recipe_id = int(request.json['recipe_id'])

    # Initialize user favorites if not exists
    if user_id not in user_favorites:
        user_favorites[user_id] = []

    if recipe_id in user_favorites[user_id]:
        user_favorites[user_id].remove(recipe_id)
        is_favorite = False
        message = 'Recipe removed from favorites'
    else:
        user_favorites[user_id].append(recipe_id)
        is_favorite = True
        message = 'Recipe added to favorites'

    return jsonify({'is_favorite': is_favorite, 'message': message})

@app.route('/correct-ingredients', methods=['POST'])
def correct_ingredients():
    """Allow users to correct detected ingredients"""
    data = request.get_json()
    corrected_ingredients = data.get('ingredients', [])

    # In a real app, you'd store this correction to improve the AI model
    # For now, we'll just return the corrected ingredients
    return jsonify({
        'success': True,
        'corrected_ingredients': corrected_ingredients,
        'message': 'Ingredients corrected successfully'
    })



@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# ML Ingredient Identification API
@app.route('/api/identify-ingredients', methods=['POST'])
def api_identify_ingredients():
    """API endpoint to identify ingredients from text"""
    try:
        data = request.get_json()
        text = data.get('text', '')

        if not text:
            return jsonify({'error': 'No text provided'}), 400

        # Use ML model to identify ingredients
        identified = identify_ingredients(text)

        return jsonify({
            'success': True,
            'ingredients': identified,
            'count': len(identified)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ingredient-suggestions', methods=['GET'])
def api_ingredient_suggestions():
    """API endpoint to get ingredient suggestions for autocomplete"""
    try:
        partial = request.args.get('q', '')
        suggestions = get_ingredient_suggestions(partial)

        return jsonify({
            'success': True,
            'suggestions': suggestions
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/generate-recipe', methods=['POST'])
def generate_recipe():
    """Generate a custom recipe using ONLY the provided ingredients"""
    try:
        user_ingredients = []

        # Handle text input
        if 'ingredients_text' in request.form and request.form['ingredients_text']:
            text_ingredients = request.form['ingredients_text'].split(',')
            user_ingredients.extend([ing.strip() for ing in text_ingredients if ing.strip()])

        # Handle file upload for ingredient detection
        if 'ingredient_image' in request.files:
            file = request.files['ingredient_image']
            if file and file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)

                # Use ML to detect ingredients from image
                detected_ingredients = identify_ingredients_from_image(filepath)
                user_ingredients.extend(detected_ingredients)

        if not user_ingredients:
            flash('Please provide some ingredients to generate a recipe!', 'error')
            return redirect(url_for('smart_recipes'))

        # Get servings from form (default to 2 if not provided)
        try:
            servings = int(request.form.get('servings', 2))
        except (ValueError, TypeError):
            servings = 2

        # Validate servings range
        if servings < 1:
            servings = 1
        elif servings > 8:
            servings = 8

        # Generate a custom recipe using only these ingredients
        generated_recipe = create_custom_recipe(user_ingredients, servings)

        return render_template('generated_recipe.html',
                             recipe=generated_recipe,
                             user_ingredients=user_ingredients)

    except Exception as e:
        flash(f'Error generating recipe: {str(e)}', 'error')
        return redirect(url_for('smart_recipes'))

def create_custom_recipe(ingredients, servings=2):
    """Create a custom recipe using only the provided ingredients"""
    import random

    # Validate servings
    if servings < 1:
        servings = 1
    elif servings > 8:
        servings = 8

    # Clean and categorize ingredients
    categorized_ingredients = categorize_user_ingredients(ingredients)

    # Determine recipe type based on available ingredients
    recipe_type = determine_recipe_type(categorized_ingredients)

    # Generate recipe name
    recipe_name = generate_recipe_name(categorized_ingredients, recipe_type)

    # Scale ingredients based on servings first
    scaled_ingredients = scale_recipe_ingredients(ingredients, servings, base_servings=2)

    # Generate cooking instructions with scaled quantities
    base_instructions = generate_custom_instructions(categorized_ingredients, recipe_type, scaled_ingredients)

    # Scale instructions for serving size if different from base (2 servings)
    if servings != 2:
        scale_factor = servings / 2
        instructions = adjust_cooking_times(base_instructions, scale_factor)
    else:
        instructions = base_instructions

    # Estimate cooking time and difficulty
    prep_time = estimate_prep_time(categorized_ingredients)
    difficulty = estimate_difficulty(categorized_ingredients)

    # Estimate calories (scale based on servings)
    base_calories = estimate_calories(categorized_ingredients)
    calories = int(base_calories * (servings / 2))

    # Determine cuisine style
    cuisine = determine_cuisine_style(categorized_ingredients)

    # Create the recipe
    recipe = {
        'id': 'custom_' + str(random.randint(1000, 9999)),
        'name': recipe_name,
        'ingredients': {
            'provided': scaled_ingredients,
            'original': ingredients  # Keep original for reference
        },
        'instructions': instructions,
        'prep_time': prep_time,
        'servings': servings,
        'calories': calories,
        'cuisine': cuisine,
        'diet_type': determine_diet_type(categorized_ingredients),
        'difficulty': difficulty,
        'tips': generate_cooking_tips(categorized_ingredients, recipe_type),
        'is_custom': True
    }

    return recipe

def categorize_user_ingredients(ingredients):
    """Categorize user ingredients into cooking categories"""
    from ingredient_ml import ingredient_identifier

    categorized = {
        'proteins': [],
        'vegetables': [],
        'grains': [],
        'dairy': [],
        'spices': [],
        'oils_fats': [],
        'condiments': [],
        'fruits': [],
        'nuts_seeds': [],
        'other': []
    }

    for ingredient in ingredients:
        ingredient_lower = ingredient.lower().strip()
        category_found = False

        for category, items in ingredient_identifier.ingredients_db.items():
            if any(item in ingredient_lower or ingredient_lower in item for item in items):
                categorized[category].append(ingredient)
                category_found = True
                break

        if not category_found:
            categorized['other'].append(ingredient)

    return categorized

def determine_recipe_type(categorized_ingredients):
    """Determine what type of dish can be made"""
    if categorized_ingredients['proteins'] and categorized_ingredients['vegetables']:
        if categorized_ingredients['grains']:
            return 'complete_meal'
        else:
            return 'protein_veggie_dish'
    elif categorized_ingredients['vegetables'] and categorized_ingredients['grains']:
        return 'vegetarian_meal'
    elif categorized_ingredients['proteins']:
        return 'protein_focused'
    elif categorized_ingredients['vegetables']:
        return 'vegetable_dish'
    elif categorized_ingredients['grains']:
        return 'grain_based'
    else:
        return 'simple_dish'

def generate_recipe_name(categorized_ingredients, recipe_type):
    """Generate cuisine-specific recipe names"""
    import random
    import re

    # Determine cuisine for naming style
    cuisine = determine_cuisine_style(categorized_ingredients)

    # Get main ingredients and clean them
    main_ingredients = []
    if categorized_ingredients['proteins']:
        main_ingredients.extend(categorized_ingredients['proteins'][:2])
    if categorized_ingredients['vegetables']:
        main_ingredients.extend(categorized_ingredients['vegetables'][:2])
    if categorized_ingredients['grains']:
        main_ingredients.extend(categorized_ingredients['grains'][:1])

    if not main_ingredients:
        main_ingredients = [ing for ing in categorized_ingredients['other'][:2]]

    # Clean ingredient names
    clean_ingredients = []
    for ing in main_ingredients:
        clean_ing = ing.lower()
        # Remove quantity words and numbers
        for word in ['cup', 'cups', 'tbsp', 'tsp', 'lb', 'lbs', 'oz', 'gram', 'grams', 'kg', 'large', 'medium', 'small', 'fresh', 'dried']:
            clean_ing = clean_ing.replace(word, '').strip()
        clean_ing = re.sub(r'\d+[\./]?\d*\s*', '', clean_ing).strip()
        if clean_ing:
            clean_ingredients.append(clean_ing.title())

    # Generate cuisine-specific names
    if cuisine == 'Indian':
        return generate_indian_name(clean_ingredients, recipe_type)
    elif cuisine == 'Chinese':
        return generate_chinese_name(clean_ingredients, recipe_type)
    elif cuisine == 'Italian':
        return generate_italian_name(clean_ingredients, recipe_type)
    elif cuisine == 'American':
        return generate_american_name(clean_ingredients, recipe_type)
    elif cuisine == 'Continental':
        return generate_continental_name(clean_ingredients, recipe_type)
    elif cuisine == 'Mexican':
        return generate_mexican_name(clean_ingredients, recipe_type)
    else:
        return generate_fusion_name(clean_ingredients, recipe_type)

def generate_indian_name(ingredients, recipe_type):
    """Generate authentic Indian recipe names"""
    import random

    if not ingredients:
        return "Mixed Vegetable Curry"

    # Get main ingredients
    proteins = [ing for ing in ingredients if any(p in ing.lower() for p in ['chicken', 'paneer', 'mutton', 'fish', 'egg', 'dal', 'lentil'])]
    vegetables = [ing for ing in ingredients if any(v in ing.lower() for v in ['potato', 'aloo', 'onion', 'tomato', 'spinach', 'palak', 'cauliflower', 'gobi', 'peas', 'matar', 'okra', 'bhindi', 'eggplant', 'baingan'])]
    grains = [ing for ing in ingredients if any(g in ing.lower() for g in ['rice', 'basmati', 'biryani'])]

    # Check for rice dishes first
    if any('rice' in ing.lower() or 'biryani' in ing.lower() for ing in grains):
        if any('chicken' in ing.lower() for ing in proteins):
            return 'Chicken Biryani'
        elif any('mutton' in ing.lower() for ing in proteins):
            return 'Mutton Biryani'
        elif any('fish' in ing.lower() for ing in proteins):
            return 'Fish Biryani'
        elif vegetables:
            return 'Vegetable Biryani'
        else:
            return 'Pulao Rice'

    # Authentic Indian dish names based on ingredients
    elif any('paneer' in ing.lower() for ing in proteins):
        paneer_dishes = [
            'Paneer Butter Masala', 'Palak Paneer', 'Paneer Tikka Masala',
            'Paneer Makhani', 'Kadai Paneer', 'Paneer Do Pyaza',
            'Shahi Paneer', 'Paneer Bhurji', 'Paneer Jalfrezi'
        ]
        return random.choice(paneer_dishes)

    elif any('chicken' in ing.lower() for ing in proteins):
        chicken_dishes = [
            'Butter Chicken', 'Chicken Tikka Masala', 'Chicken Curry',
            'Chicken Masala', 'Kadai Chicken', 'Chicken Do Pyaza',
            'Chicken Jalfrezi', 'Chicken Korma', 'Chicken Vindaloo'
        ]
        return random.choice(chicken_dishes)

    elif any('dal' in ing.lower() or 'lentil' in ing.lower() for ing in proteins):
        dal_dishes = [
            'Dal Tadka', 'Dal Makhani', 'Moong Dal', 'Masoor Dal',
            'Chana Dal', 'Toor Dal', 'Mixed Dal Curry'
        ]
        return random.choice(dal_dishes)

    elif any(veg in ing.lower() for veg in ['potato', 'aloo'] for ing in vegetables):
        potato_dishes = [
            'Aloo Gobi', 'Aloo Matar', 'Jeera Aloo', 'Bombay Aloo',
            'Aloo Palak', 'Dum Aloo', 'Aloo Bhaji', 'Aloo Curry'
        ]
        return random.choice(potato_dishes)

    elif any(veg in ing.lower() for veg in ['spinach', 'palak'] for ing in vegetables):
        spinach_dishes = [
            'Palak Paneer', 'Saag Aloo', 'Palak Dal', 'Palak Chicken',
            'Sarson Ka Saag', 'Palak Curry'
        ]
        return random.choice(spinach_dishes)

    elif any(veg in ing.lower() for veg in ['cauliflower', 'gobi'] for ing in vegetables):
        cauliflower_dishes = [
            'Aloo Gobi', 'Gobi Masala', 'Gobi Matar',
            'Dry Gobi Sabzi', 'Gobi Curry'
        ]
        return random.choice(cauliflower_dishes)

    elif vegetables and len(vegetables) >= 2:
        mixed_dishes = [
            'Mixed Vegetable Curry', 'Sabzi Masala', 'Vegetable Jalfrezi',
            'Seasonal Vegetable Curry', 'Garden Fresh Curry', 'Mixed Sabzi'
        ]
        return random.choice(mixed_dishes)

    else:
        # Improved fallback names
        generic_dishes = [
            'Homestyle Curry', 'Masala Curry', 'Tadka Dal', 'Simple Sabzi',
            'Indian Spiced Vegetables', 'Desi Style Curry'
        ]
        return random.choice(generic_dishes)

def generate_chinese_name(ingredients, recipe_type):
    """Generate authentic Chinese recipe names"""
    import random

    if not ingredients:
        return "Mixed Vegetable Stir Fry"

    # Get main ingredients
    proteins = [ing for ing in ingredients if any(p in ing.lower() for p in ['chicken', 'beef', 'pork', 'shrimp', 'fish', 'tofu', 'egg'])]
    vegetables = [ing for ing in ingredients if any(v in ing.lower() for v in ['broccoli', 'pepper', 'onion', 'carrot', 'mushroom', 'bok choy', 'snow pea', 'bean sprout', 'cabbage', 'scallion'])]
    grains = [ing for ing in ingredients if any(g in ing.lower() for g in ['rice', 'noodle', 'pasta'])]

    # Check for rice dishes first
    if any('rice' in ing.lower() for ing in grains):
        if proteins:
            if any('chicken' in ing.lower() for ing in proteins):
                return 'Chicken Fried Rice'
            elif any('beef' in ing.lower() for ing in proteins):
                return 'Beef Fried Rice'
            elif any('shrimp' in ing.lower() for ing in proteins):
                return 'Shrimp Fried Rice'
            elif any('egg' in ing.lower() for ing in proteins):
                return 'Egg Fried Rice'
            else:
                return 'Special Fried Rice'
        elif vegetables:
            return 'Vegetable Fried Rice'
        else:
            return 'Simple Fried Rice'

    # Check for noodle dishes
    elif any('noodle' in ing.lower() for ing in grains):
        if proteins:
            if any('chicken' in ing.lower() for ing in proteins):
                return 'Chicken Lo Mein'
            elif any('beef' in ing.lower() for ing in proteins):
                return 'Beef Chow Mein'
            elif any('shrimp' in ing.lower() for ing in proteins):
                return 'Shrimp Lo Mein'
            else:
                return 'Mixed Meat Noodles'
        else:
            return 'Vegetable Lo Mein'

    # Protein-based dishes
    elif any('chicken' in ing.lower() for ing in proteins):
        chicken_dishes = [
            'Kung Pao Chicken', 'General Tso\'s Chicken', 'Sweet and Sour Chicken',
            'Orange Chicken', 'Cashew Chicken', 'Sichuan Chicken',
            'Honey Garlic Chicken', 'Black Pepper Chicken', 'Teriyaki Chicken'
        ]
        return random.choice(chicken_dishes)

    elif any('beef' in ing.lower() for ing in proteins):
        beef_dishes = [
            'Mongolian Beef', 'Orange Beef', 'Black Pepper Beef',
            'Sichuan Beef', 'Beef and Broccoli', 'Kung Pao Beef',
            'Honey Garlic Beef', 'Beijing Beef'
        ]
        return random.choice(beef_dishes)

    elif any('pork' in ing.lower() for ing in proteins):
        pork_dishes = [
            'Sweet and Sour Pork', 'Char Siu Pork', 'Sichuan Pork',
            'Honey Garlic Pork', 'Five Spice Pork', 'Twice Cooked Pork'
        ]
        return random.choice(pork_dishes)

    elif any('shrimp' in ing.lower() for ing in proteins):
        shrimp_dishes = [
            'Honey Walnut Shrimp', 'Kung Pao Shrimp', 'Sweet and Sour Shrimp',
            'Sichuan Shrimp', 'Garlic Shrimp', 'Salt and Pepper Shrimp'
        ]
        return random.choice(shrimp_dishes)

    elif any('tofu' in ing.lower() for ing in proteins):
        tofu_dishes = [
            'Mapo Tofu', 'Sichuan Tofu', 'Salt and Pepper Tofu',
            'Braised Tofu', 'Kung Pao Tofu', 'General Tso\'s Tofu'
        ]
        return random.choice(tofu_dishes)

    elif vegetables and len(vegetables) >= 2:
        vegetable_dishes = [
            'Mixed Vegetable Stir Fry', 'Buddha\'s Delight', 'Sichuan Vegetables',
            'Garlic Vegetables', 'Chinese Mixed Vegetables', 'Seasonal Stir Fry'
        ]
        return random.choice(vegetable_dishes)

    else:
        # Improved fallback names
        generic_dishes = [
            'Chinese Stir Fry', 'Wok Fried Vegetables', 'House Style Stir Fry',
            'Traditional Chinese Dish', 'Chinese Style Mixed Vegetables'
        ]
        return random.choice(generic_dishes)

def generate_italian_name(ingredients, recipe_type):
    """Generate authentic Italian recipe names"""
    import random

    if not ingredients:
        return "Pasta Primavera"

    # Get main ingredients
    proteins = [ing for ing in ingredients if any(p in ing.lower() for p in ['chicken', 'beef', 'pork', 'fish', 'seafood', 'prosciutto', 'pancetta'])]
    vegetables = [ing for ing in ingredients if any(v in ing.lower() for v in ['tomato', 'basil', 'spinach', 'mushroom', 'zucchini', 'eggplant', 'pepper', 'onion'])]
    pasta_types = [ing for ing in ingredients if any(p in ing.lower() for p in ['pasta', 'spaghetti', 'penne', 'fettuccine', 'linguine', 'rigatoni'])]

    # Authentic Italian dish names
    if pasta_types:
        pasta_dishes = [
            'Spaghetti Carbonara', 'Pasta Arrabbiata', 'Fettuccine Alfredo',
            'Penne all\'Arrabbiata', 'Linguine alle Vongole', 'Pasta Puttanesca',
            'Spaghetti Aglio e Olio', 'Pasta Marinara', 'Cacio e Pepe'
        ]
        return random.choice(pasta_dishes)

    elif any('chicken' in ing.lower() for ing in proteins):
        chicken_dishes = [
            'Pollo Parmigiana', 'Chicken Piccata', 'Pollo alla Cacciatora',
            'Chicken Marsala', 'Pollo alla Griglia', 'Chicken Saltimbocca'
        ]
        return random.choice(chicken_dishes)

    elif any('fish' in ing.lower() for ing in proteins):
        fish_dishes = [
            'Pesce alla Griglia', 'Fish Piccata', 'Branzino al Sale',
            'Salmon Puttanesca', 'Italian Baked Fish'
        ]
        return random.choice(fish_dishes)

    elif any(veg in ing.lower() for veg in ['eggplant', 'melanzane'] for ing in vegetables):
        eggplant_dishes = [
            'Melanzane Parmigiana', 'Eggplant Caponata', 'Melanzane alla Griglia'
        ]
        return random.choice(eggplant_dishes)

    elif any(veg in ing.lower() for veg in ['tomato', 'pomodoro'] for ing in vegetables):
        tomato_dishes = [
            'Pomodoro e Basilico', 'Caprese Style', 'Pomodori Ripieni',
            'Italian Tomato Salad', 'Bruschetta Topping'
        ]
        return random.choice(tomato_dishes)

    elif vegetables and len(vegetables) >= 2:
        vegetable_dishes = [
            'Verdure alla Griglia', 'Caponata Siciliana', 'Minestrone Style',
            'Italian Garden Vegetables', 'Contorno Misto'
        ]
        return random.choice(vegetable_dishes)

    else:
        # Fallback to generic Italian names
        generic_dishes = [
            'Piatto Italiano', 'Casa Special', 'Nonna\'s Recipe',
            'Italian Style', 'Rustico Italiano'
        ]
        return random.choice(generic_dishes)

def generate_american_name(ingredients, recipe_type):
    """Generate authentic American recipe names"""
    import random

    if not ingredients:
        return "All-American Skillet"

    # Get main ingredients
    proteins = [ing for ing in ingredients if any(p in ing.lower() for p in ['beef', 'chicken', 'pork', 'turkey', 'bacon', 'sausage', 'ground beef'])]
    vegetables = [ing for ing in ingredients if any(v in ing.lower() for v in ['potato', 'corn', 'onion', 'pepper', 'mushroom', 'tomato'])]

    # Authentic American dish names
    if any('ground beef' in ing.lower() or 'beef' in ing.lower() for ing in proteins):
        beef_dishes = [
            'Classic Beef Stroganoff', 'American Beef Stew', 'Hearty Beef Skillet',
            'Country Fried Steak', 'Beef and Mushroom Skillet', 'All-American Meatloaf',
            'Cowboy Beef Hash', 'Salisbury Steak'
        ]
        return random.choice(beef_dishes)

    elif any('chicken' in ing.lower() for ing in proteins):
        chicken_dishes = [
            'Southern Fried Chicken', 'Chicken and Dumplings', 'Buffalo Chicken Skillet',
            'BBQ Chicken Platter', 'Chicken Pot Pie Style', 'Ranch Chicken Bake',
            'Honey Mustard Chicken', 'American Chicken Casserole'
        ]
        return random.choice(chicken_dishes)

    elif any('pork' in ing.lower() for ing in proteins):
        pork_dishes = [
            'BBQ Pulled Pork', 'Southern Pork Chops', 'Honey Glazed Pork',
            'American Pork Skillet', 'Country Style Ribs'
        ]
        return random.choice(pork_dishes)

    elif any('bacon' in ing.lower() for ing in proteins):
        bacon_dishes = [
            'Bacon and Egg Skillet', 'Loaded Bacon Hash', 'Bacon Ranch Potatoes',
            'Country Bacon Breakfast'
        ]
        return random.choice(bacon_dishes)

    elif any(veg in ing.lower() for veg in ['potato'] for ing in vegetables):
        potato_dishes = [
            'Loaded Potato Skillet', 'American Hash Browns', 'Country Potato Bake',
            'Cheesy Potato Casserole', 'Cowboy Potato Hash'
        ]
        return random.choice(potato_dishes)

    elif vegetables and len(vegetables) >= 2:
        vegetable_dishes = [
            'Garden Fresh Skillet', 'American Vegetable Medley', 'Farmers Market Hash',
            'Country Garden Vegetables', 'Harvest Vegetable Bake'
        ]
        return random.choice(vegetable_dishes)

    else:
        # Fallback to generic American names
        generic_dishes = [
            'All-American Special', 'Country Kitchen Style', 'Homestyle Comfort Food',
            'American Classic', 'Heartland Special'
        ]
        return random.choice(generic_dishes)

def generate_continental_name(ingredients, recipe_type):
    """Generate authentic Continental recipe names"""
    import random

    if not ingredients:
        return "Continental Medley"

    # Get main ingredients
    proteins = [ing for ing in ingredients if any(p in ing.lower() for p in ['chicken', 'beef', 'fish', 'salmon', 'duck', 'lamb'])]
    vegetables = [ing for ing in ingredients if any(v in ing.lower() for v in ['mushroom', 'asparagus', 'leek', 'shallot', 'spinach', 'artichoke'])]

    # Authentic Continental dish names
    if any('chicken' in ing.lower() for ing in proteins):
        chicken_dishes = [
            'Chicken Cordon Bleu', 'Coq au Vin', 'Chicken Chasseur',
            'Chicken à la King', 'Chicken Fricassee', 'Chicken Marsala',
            'Chicken Piccata', 'Chicken Wellington'
        ]
        return random.choice(chicken_dishes)

    elif any('beef' in ing.lower() for ing in proteins):
        beef_dishes = [
            'Beef Bourguignon', 'Beef Stroganoff', 'Beef Wellington',
            'Steak au Poivre', 'Beef Medallions', 'Beef Tenderloin',
            'Beef Roulade', 'Beef Ragout'
        ]
        return random.choice(beef_dishes)

    elif any('fish' in ing.lower() or 'salmon' in ing.lower() for ing in proteins):
        fish_dishes = [
            'Salmon en Papillote', 'Fish Meunière', 'Sole Almondine',
            'Salmon Terrine', 'Fish Véronique', 'Poached Salmon'
        ]
        return random.choice(fish_dishes)

    elif any('lamb' in ing.lower() for ing in proteins):
        lamb_dishes = [
            'Lamb Provençal', 'Rack of Lamb', 'Lamb Navarin',
            'Lamb Medallions', 'Braised Lamb Shanks'
        ]
        return random.choice(lamb_dishes)

    elif vegetables and len(vegetables) >= 2:
        vegetable_dishes = [
            'Ratatouille Niçoise', 'Vegetable Gratin', 'Seasonal Vegetables',
            'Garden Vegetable Medley', 'Continental Vegetable Tart'
        ]
        return random.choice(vegetable_dishes)

    else:
        # Fallback to generic Continental names
        generic_dishes = [
            'Continental Classic', 'European Style', 'Chef\'s Special',
            'Continental Cuisine', 'Bistro Style'
        ]
        return random.choice(generic_dishes)

def generate_mexican_name(ingredients, recipe_type):
    """Generate authentic Mexican recipe names"""
    import random

    if not ingredients:
        return "Mexican Fiesta Bowl"

    # Get main ingredients
    proteins = [ing for ing in ingredients if any(p in ing.lower() for p in ['chicken', 'beef', 'pork', 'fish', 'shrimp', 'chorizo'])]
    vegetables = [ing for ing in ingredients if any(v in ing.lower() for v in ['pepper', 'onion', 'tomato', 'corn', 'avocado', 'jalapeño', 'cilantro'])]

    # Authentic Mexican dish names
    if any('chicken' in ing.lower() for ing in proteins):
        chicken_dishes = [
            'Pollo a la Mexicana', 'Chicken Fajitas', 'Pollo en Mole',
            'Chicken Enchiladas', 'Pollo Asado', 'Chicken Quesadillas',
            'Arroz con Pollo', 'Pollo Ranchero'
        ]
        return random.choice(chicken_dishes)

    elif any('beef' in ing.lower() for ing in proteins):
        beef_dishes = [
            'Carne Asada', 'Beef Fajitas', 'Picadillo',
            'Beef Tacos', 'Carne Guisada', 'Mexican Beef Stew'
        ]
        return random.choice(beef_dishes)

    elif any('pork' in ing.lower() for ing in proteins):
        pork_dishes = [
            'Carnitas', 'Chile Verde', 'Pork Tacos',
            'Cochinita Pibil', 'Al Pastor Style'
        ]
        return random.choice(pork_dishes)

    elif any('fish' in ing.lower() or 'shrimp' in ing.lower() for ing in proteins):
        seafood_dishes = [
            'Fish Tacos', 'Camarones a la Diabla', 'Pescado Veracruzana',
            'Shrimp Fajitas', 'Ceviche Style'
        ]
        return random.choice(seafood_dishes)

    elif vegetables and len(vegetables) >= 2:
        vegetable_dishes = [
            'Vegetable Fajitas', 'Mexican Vegetable Medley', 'Verduras a la Mexicana',
            'Rajas con Crema', 'Mexican Garden Vegetables'
        ]
        return random.choice(vegetable_dishes)

    else:
        # Fallback to generic Mexican names
        generic_dishes = [
            'Mexican Fiesta', 'Cantina Special', 'Casa Style',
            'Mexican Comfort Food', 'Hacienda Special'
        ]
        return random.choice(generic_dishes)

def generate_fusion_name(ingredients, recipe_type):
    """Generate creative fusion recipe names"""
    import random

    if not ingredients:
        return "Global Fusion Bowl"

    # Get main ingredients
    proteins = [ing for ing in ingredients if any(p in ing.lower() for p in ['chicken', 'beef', 'fish', 'tofu', 'shrimp'])]
    vegetables = [ing for ing in ingredients if any(v in ing.lower() for v in ['mushroom', 'pepper', 'onion', 'broccoli', 'spinach'])]

    # Creative fusion dish names
    if any('chicken' in ing.lower() for ing in proteins):
        fusion_dishes = [
            'Asian-Inspired Chicken Bowl', 'Mediterranean Chicken Fusion', 'Global Chicken Stir-Fry',
            'East-West Chicken Medley', 'Modern Chicken Creation', 'International Chicken Skillet'
        ]
        return random.choice(fusion_dishes)

    elif any('beef' in ing.lower() for ing in proteins):
        fusion_dishes = [
            'Pan-Asian Beef Bowl', 'Mediterranean Beef Fusion', 'Global Beef Stir-Fry',
            'Contemporary Beef Medley', 'International Beef Skillet'
        ]
        return random.choice(fusion_dishes)

    elif any('fish' in ing.lower() for ing in proteins):
        fusion_dishes = [
            'Asian-Mediterranean Fish', 'Global Fish Creation', 'East-West Fish Bowl',
            'Modern Fish Fusion', 'International Fish Medley'
        ]
        return random.choice(fusion_dishes)

    elif vegetables and len(vegetables) >= 2:
        vegetable_dishes = [
            'Global Vegetable Fusion', 'International Garden Bowl', 'World Vegetable Medley',
            'East-West Vegetable Stir-Fry', 'Modern Vegetable Creation'
        ]
        return random.choice(vegetable_dishes)

    else:
        # Fallback to generic fusion names
        generic_dishes = [
            'Global Fusion Special', 'International Medley', 'World Cuisine Bowl',
            'East-West Creation', 'Modern Fusion Dish'
        ]
        return random.choice(generic_dishes)

def generate_custom_instructions(categorized_ingredients, recipe_type, scaled_ingredients=None):
    """Generate detailed cooking instructions based on available ingredients and cuisine"""
    # Determine cuisine for cooking style
    cuisine = determine_cuisine_style(categorized_ingredients)

    # Extract quantities if scaled ingredients are provided
    quantities = {}
    if scaled_ingredients:
        quantities = extract_ingredient_quantities(scaled_ingredients)

    # Get detailed instructions based on cuisine and ingredients
    if cuisine == 'Indian':
        return generate_indian_instructions(categorized_ingredients, recipe_type, quantities)
    elif cuisine == 'Chinese':
        return generate_chinese_instructions(categorized_ingredients, recipe_type, quantities)
    elif cuisine == 'Italian':
        return generate_italian_instructions(categorized_ingredients, recipe_type, quantities)
    elif cuisine == 'American':
        return generate_american_instructions(categorized_ingredients, recipe_type, quantities)
    elif cuisine == 'Continental':
        return generate_continental_instructions(categorized_ingredients, recipe_type, quantities)
    elif cuisine == 'Mexican':
        return generate_mexican_instructions(categorized_ingredients, recipe_type, quantities)
    else:
        return generate_fusion_instructions(categorized_ingredients, recipe_type, quantities)

def generate_indian_instructions(categorized_ingredients, recipe_type, quantities=None):
    """Generate detailed Indian cooking instructions with specific quantities"""
    instructions = []

    if quantities is None:
        quantities = {}

    # Get default quantities for this recipe
    servings = 2  # Default, will be overridden by actual servings
    if quantities:
        # Try to estimate servings from ingredient quantities
        for key, info in quantities.items():
            if 'rice' in key and info['quantity']:
                servings = max(2, int(info['quantity'] / 1))  # Rough estimate
                break

    defaults = get_default_cooking_quantities(servings)

    # Preparation phase
    instructions.append("🔪 PREPARATION PHASE:")
    instructions.append("• Wash all vegetables thoroughly under running water")

    if categorized_ingredients['vegetables']:
        # Add specific quantities for preparation
        onion_ref = get_ingredient_reference("onion", quantities, default_quantity=f"{servings} medium onions")
        garlic_ref = get_ingredient_reference("garlic", quantities, default_quantity=defaults['garlic'])
        ginger_ref = get_ingredient_reference("ginger", quantities, default_quantity=defaults['ginger'])

        instructions.append(f"• Finely chop {onion_ref}, mince {garlic_ref} and {ginger_ref}")
        instructions.append("• Cut vegetables into uniform pieces - potatoes in cubes, tomatoes chopped, green vegetables sliced")
        instructions.append("• Keep all ingredients ready as Indian cooking moves quickly once started")

    if categorized_ingredients['proteins']:
        protein_ref = get_ingredient_reference("chicken", quantities) or get_ingredient_reference("paneer", quantities) or "protein"
        instructions.append(f"• Clean {protein_ref} thoroughly, pat dry with paper towels")

        turmeric_ref = get_ingredient_reference("turmeric", quantities, 0.25) if quantities else "1/2 tsp turmeric"
        instructions.append(f"• Cut into bite-sized pieces and marinate with salt, {turmeric_ref}, and available spices for 15 minutes")

    if categorized_ingredients['spices']:
        masala_ref = get_ingredient_reference("garam masala", quantities, 0.5) if quantities else "spices"
        instructions.append(f"• Prepare spice mix by combining {masala_ref} and other dry spices in a small bowl")
        instructions.append("• Keep whole spices (if any) separate for tempering")

    # Cooking phase
    instructions.append("\n🔥 COOKING PHASE:")
    oil_ref = get_ingredient_reference("oil", quantities) or get_ingredient_reference("ghee", quantities) or "2-3 tbsp oil"
    instructions.append(f"• Heat {oil_ref} in a heavy-bottomed pan over medium heat")
    instructions.append("• Test oil temperature by dropping a small piece of onion - it should sizzle immediately")

    if 'cumin' in ' '.join(categorized_ingredients.get('spices', [])).lower():
        cumin_ref = get_ingredient_reference("cumin", quantities, 0.1) if quantities else "1 tsp cumin seeds"
        instructions.append(f"• Add {cumin_ref} and let them splutter for 10-15 seconds")

    onion_ref = get_ingredient_reference("onion", quantities) if quantities else "chopped onions"
    instructions.append(f"• Add {onion_ref} and sauté until golden brown (5-7 minutes)")

    garlic_ginger = "minced garlic and ginger"
    if quantities:
        garlic_ref = get_ingredient_reference("garlic", quantities, 0.5)
        ginger_ref = get_ingredient_reference("ginger", quantities, 0.5)
        garlic_ginger = f"{garlic_ref} and {ginger_ref}"
    instructions.append(f"• Add {garlic_ginger}, cook for 1 minute until fragrant")

    if categorized_ingredients['proteins']:
        instructions.append("• Add marinated protein pieces and cook on high heat for 3-4 minutes")
        instructions.append("• Stir frequently to ensure even browning on all sides")

    if categorized_ingredients['vegetables'] and any('tomato' in veg.lower() for veg in categorized_ingredients['vegetables']):
        tomato_ref = get_ingredient_reference("tomato", quantities) if quantities else "tomatoes"
        instructions.append(f"• Add {tomato_ref} and cook until they break down and become mushy")

    spice_ref = "the prepared spice mix"
    if quantities:
        masala_ref = get_ingredient_reference("garam masala", quantities, 0.5)
        if masala_ref != "garam masala":
            spice_ref = f"{masala_ref} and other spices"
    instructions.append(f"• Add {spice_ref} and cook for 2 minutes, stirring constantly to prevent burning")

    if categorized_ingredients['vegetables']:
        instructions.append("• Add harder vegetables first (potatoes, carrots) and cook for 5 minutes")
        instructions.append("• Add softer vegetables (peas, spinach) and mix gently")

    water_ref = get_ingredient_reference("water", quantities, default_quantity=defaults['water'])
    instructions.append(f"• Add {water_ref} if the mixture looks too dry")
    instructions.append("• Cover and simmer on low heat for 10-15 minutes until everything is cooked through")

    # Finishing
    instructions.append("\n✨ FINISHING TOUCHES:")
    salt_ref = get_ingredient_reference("salt", quantities, default_quantity=defaults['salt'])
    instructions.append(f"• Taste and adjust {salt_ref} and spices as needed")
    instructions.append("• Garnish with fresh cilantro or mint leaves if available")
    instructions.append("• Serve hot with rice, roti, or bread")

    ghee_ref = get_ingredient_reference("ghee", quantities, default_quantity=defaults['ghee'])
    instructions.append(f"• Drizzle with {ghee_ref} for extra flavor")

    return instructions

def generate_chinese_instructions(categorized_ingredients, recipe_type, quantities=None):
    """Generate detailed Chinese cooking instructions with specific quantities"""
    instructions = []

    if quantities is None:
        quantities = {}

    # Get default quantities for this recipe
    servings = 2  # Default
    if quantities:
        # Try to estimate servings from ingredient quantities
        for key, info in quantities.items():
            if info['quantity'] and info['quantity'] > 1:
                servings = max(2, int(info['quantity']))
                break

    defaults = get_default_cooking_quantities(servings)

    instructions.append("🔪 PREPARATION PHASE (Mise en Place):")
    instructions.append("• Prepare all ingredients before starting - Chinese cooking is fast!")

    if categorized_ingredients['vegetables']:
        instructions.append("• Cut vegetables into uniform sizes for even cooking")
        instructions.append("• Slice vegetables diagonally for better presentation and cooking")
        instructions.append("• Keep harder vegetables (carrots, broccoli) separate from softer ones")

    if categorized_ingredients['proteins']:
        protein_ref = get_ingredient_reference("chicken", quantities) or get_ingredient_reference("beef", quantities) or f"{servings} lb protein"
        soy_sauce_ref = get_ingredient_reference("soy sauce", quantities, 0.4, default_quantity=f"{servings} tbsp soy sauce")
        cornstarch_ref = get_ingredient_reference("cornstarch", quantities, default_quantity=defaults['cornstarch'])
        salt_ref = get_ingredient_reference("salt", quantities, 0.2, default_quantity="1/2 tsp salt")

        instructions.append(f"• Slice {protein_ref} against the grain into thin, even pieces")
        instructions.append(f"• Marinate with {soy_sauce_ref}, {cornstarch_ref}, and {salt_ref} for 15 minutes")

    remaining_soy = get_ingredient_reference("soy sauce", quantities, 0.6) if quantities else "remaining soy sauce"
    instructions.append(f"• Prepare sauce by mixing {remaining_soy} with available seasonings")
    instructions.append("• Have all ingredients within arm's reach of the stove")

    instructions.append("\n🔥 STIR-FRY TECHNIQUE:")
    oil_ref = get_ingredient_reference("oil", quantities) or "2-3 tbsp oil"
    instructions.append("• Heat wok or large skillet over high heat until smoking")
    instructions.append(f"• Add {oil_ref} and swirl to coat the entire surface")
    instructions.append("• The oil should shimmer and move freely when the pan is tilted")

    if categorized_ingredients['proteins']:
        instructions.append("• Add protein in a single layer, don't overcrowd")
        instructions.append("• Let it sear for 1-2 minutes without stirring for good color")
        instructions.append("• Stir-fry rapidly for 2-3 minutes until almost cooked, then remove")

    garlic_ref = get_ingredient_reference("garlic", quantities, 0.3, default_quantity=defaults['garlic'])
    ginger_ref = get_ingredient_reference("ginger", quantities, 0.3, default_quantity=defaults['ginger'])
    instructions.append(f"• Add {garlic_ref} and {ginger_ref}, stir for 10 seconds until fragrant")
    instructions.append("• Add harder vegetables first, stir-fry for 2 minutes")
    instructions.append("• Add softer vegetables and continue stir-frying")
    instructions.append("• Keep ingredients moving constantly to prevent burning")

    if categorized_ingredients['proteins']:
        instructions.append("• Return protein to the wok and toss everything together")

    remaining_soy = get_ingredient_reference("soy sauce", quantities, 0.6, default_quantity=f"{servings} tbsp soy sauce")
    instructions.append(f"• Add {remaining_soy} and prepared sauce, toss for 30 seconds")
    instructions.append("• Everything should be heated through and well-coated")

    instructions.append("\n✨ FINAL STEPS:")
    salt_ref = get_ingredient_reference("salt", quantities, 0.5, default_quantity=defaults['salt'])
    instructions.append(f"• Taste and adjust seasoning with additional soy sauce or {salt_ref}")
    instructions.append("• Serve immediately while hot and crispy")
    instructions.append("• Garnish with sliced scallions or sesame seeds if available")

    return instructions

def generate_italian_instructions(categorized_ingredients, recipe_type, quantities=None):
    """Generate detailed Italian cooking instructions with specific quantities"""
    instructions = []

    if quantities is None:
        quantities = {}

    instructions.append("🔪 PREPARATION (Preparazione):")
    instructions.append("• Use the freshest ingredients possible - quality is key in Italian cooking")

    if categorized_ingredients['vegetables']:
        instructions.append("• Dice onions finely, slice garlic thinly (never crush - it becomes bitter)")
        instructions.append("• If using tomatoes, score an X on the bottom, blanch in boiling water for 1 minute, then peel")
        instructions.append("• Cut vegetables into rustic, irregular pieces for authentic Italian style")

    if categorized_ingredients['proteins']:
        instructions.append("• Bring protein to room temperature 30 minutes before cooking")
        instructions.append("• Season generously with salt and freshly ground black pepper")

    instructions.append("• Warm your serving plates - Italian food should be served hot")

    instructions.append("\n🔥 COOKING TECHNIQUE:")
    instructions.append("• Heat good quality olive oil in a heavy-bottomed pan over medium heat")
    instructions.append("• The oil should be warm but not smoking - Italian cooking is gentle")

    instructions.append("• Add garlic and cook slowly until fragrant (30 seconds)")
    instructions.append("• Never let garlic brown - it will make the dish bitter")

    if categorized_ingredients['proteins']:
        instructions.append("• Add protein and cook gently, turning once for even browning")
        instructions.append("• Don't move the protein too much - let it develop a nice crust")

    if 'tomato' in ' '.join(categorized_ingredients.get('vegetables', [])).lower():
        instructions.append("• Add tomatoes and cook until they start to break down")
        instructions.append("• Season with salt to help release the tomato juices")

    instructions.append("• Add other vegetables and cook until tender but still with some bite")
    instructions.append("• Season with salt, pepper, and fresh herbs if available")

    instructions.append("\n✨ FINISHING (Mantecatura):")
    instructions.append("• Taste and adjust seasoning - Italian food should be well-seasoned")
    instructions.append("• Drizzle with your best olive oil just before serving")
    instructions.append("• Add fresh herbs like basil or parsley at the very end")
    instructions.append("• Serve immediately with crusty bread or pasta if available")

    return instructions

def generate_american_instructions(categorized_ingredients, recipe_type, quantities=None):
    """Generate detailed American cooking instructions with specific quantities"""
    instructions = []

    if quantities is None:
        quantities = {}

    instructions.append("🔪 PREP WORK:")
    instructions.append("• Gather all ingredients and equipment before starting")
    instructions.append("• Preheat your cooking surface - Americans love high heat cooking")

    if categorized_ingredients['vegetables']:
        instructions.append("• Chop vegetables into hearty, generous pieces")
        instructions.append("• Keep onions separate - they'll go in first for that sweet caramelized flavor")

    if categorized_ingredients['proteins']:
        instructions.append("• Pat protein completely dry with paper towels for better searing")
        instructions.append("• Season liberally with salt, pepper, and any available spices")
        instructions.append("• Let seasoned protein sit for 10 minutes to absorb flavors")

    instructions.append("\n🔥 COOKING METHOD:")
    instructions.append("• Heat oil or butter in a large skillet over medium-high heat")
    instructions.append("• The pan should be hot enough that a drop of water sizzles immediately")

    if categorized_ingredients['proteins']:
        instructions.append("• Sear protein without moving it for 3-4 minutes per side")
        instructions.append("• You want a nice golden-brown crust - this adds tons of flavor")
        instructions.append("• Remove protein and let it rest while cooking vegetables")

    instructions.append("• Add onions to the same pan and cook until softened and lightly browned")
    instructions.append("• Scrape up any browned bits from the bottom - that's pure flavor!")
    instructions.append("• Add other vegetables in order of cooking time needed")
    instructions.append("• Season each layer as you go for maximum flavor")

    if categorized_ingredients['proteins']:
        instructions.append("• Return protein to the pan and heat through")

    instructions.append("\n✨ FINAL TOUCHES:")
    instructions.append("• Taste and season generously - American food is bold and flavorful")
    instructions.append("• Add a pat of butter at the end for richness (if available)")
    instructions.append("• Serve hot with your favorite sides")
    instructions.append("• Don't forget to enjoy with good company!")

    return instructions

def generate_continental_instructions(categorized_ingredients, recipe_type, quantities=None):
    """Generate detailed Continental cooking instructions with specific quantities"""
    instructions = []

    if quantities is None:
        quantities = {}

    instructions.append("🔪 MISE EN PLACE:")
    instructions.append("• Prepare all ingredients with precision - Continental cooking values technique")
    instructions.append("• Cut vegetables into uniform, elegant pieces")

    if categorized_ingredients['vegetables']:
        instructions.append("• Brunoise (fine dice) the onions for a refined texture")
        instructions.append("• Cut vegetables into classical shapes - julienne, batonnet, or dice")

    if categorized_ingredients['proteins']:
        instructions.append("• Trim protein of excess fat and sinew for clean presentation")
        instructions.append("• Season with salt and white pepper (if available) for subtle flavor")

    instructions.append("\n🔥 COOKING TECHNIQUE:")
    instructions.append("• Use moderate heat and patience - Continental cooking is about control")
    instructions.append("• Heat butter and oil together in a heavy pan over medium heat")
    instructions.append("• The butter adds flavor while oil prevents burning")

    instructions.append("• Sauté aromatics gently until translucent, not browned")
    instructions.append("• This creates a flavor base without overpowering the dish")

    if categorized_ingredients['proteins']:
        instructions.append("• Sear protein gently on both sides until golden")
        instructions.append("• Cook to proper doneness - use a thermometer if available")

    instructions.append("• Add vegetables in stages based on cooking time")
    instructions.append("• Each ingredient should retain its individual character")

    if categorized_ingredients['dairy']:
        instructions.append("• Add cream or dairy at the end, off the heat to prevent curdling")
        instructions.append("• Stir gently to create a silky, elegant sauce")

    instructions.append("\n✨ PRESENTATION:")
    instructions.append("• Arrange components thoughtfully on warmed plates")
    instructions.append("• Continental cuisine values visual appeal as much as taste")
    instructions.append("• Finish with fresh herbs or a light drizzle of good oil")
    instructions.append("• Serve immediately while at optimal temperature")

    return instructions

def generate_mexican_instructions(categorized_ingredients, recipe_type, quantities=None):
    """Generate detailed Mexican cooking instructions with specific quantities"""
    instructions = []

    if quantities is None:
        quantities = {}

    instructions.append("🔪 PREPARACIÓN:")
    instructions.append("• Prepare ingredients with love - Mexican cooking is about family and tradition")

    if categorized_ingredients['vegetables']:
        instructions.append("• Dice onions and tomatoes, mince garlic finely")
        instructions.append("• If using chiles, remove seeds for less heat or keep them for more spice")
        instructions.append("• Chop cilantro roughly - stems have flavor too!")

    if categorized_ingredients['proteins']:
        instructions.append("• Cut protein into bite-sized pieces for even cooking")
        instructions.append("• Season with salt, cumin, and chili powder if available")

    instructions.append("\n🔥 COOKING TECHNIQUE:")
    instructions.append("• Heat oil in a large, heavy skillet over medium-high heat")
    instructions.append("• Mexican cooking often uses higher heat for bold flavors")

    instructions.append("• Start with onions and cook until they're soft and fragrant")
    instructions.append("• Add garlic and cook for just 30 seconds - don't let it burn")

    if categorized_ingredients['proteins']:
        instructions.append("• Add protein and cook, stirring frequently until browned")
        instructions.append("• Let it get some color - this adds depth to the dish")

    instructions.append("• Add tomatoes and cook until they start to break down")
    instructions.append("• Season with cumin, chili powder, and salt")
    instructions.append("• Add other vegetables and cook until tender")

    instructions.append("\n✨ FINISHING TOUCHES:")
    instructions.append("• Taste and adjust seasoning - Mexican food should be vibrant and well-spiced")
    instructions.append("• Add fresh lime juice at the end for brightness")
    instructions.append("• Garnish with fresh cilantro and serve immediately")
    instructions.append("• Serve with warm tortillas, rice, or beans if available")

    return instructions

def generate_fusion_instructions(categorized_ingredients, recipe_type, quantities=None):
    """Generate detailed fusion cooking instructions with specific quantities"""
    instructions = []

    if quantities is None:
        quantities = {}

    instructions.append("🔪 CREATIVE PREPARATION:")
    instructions.append("• Fusion cooking is about combining techniques - be creative!")
    instructions.append("• Prepare ingredients using the best technique for each component")

    if categorized_ingredients['vegetables']:
        instructions.append("• Cut vegetables in varied shapes for visual interest")
        instructions.append("• Consider both texture and cooking time when preparing")

    if categorized_ingredients['proteins']:
        instructions.append("• Season protein with a blend of available spices")
        instructions.append("• Consider marinating for 15-20 minutes if time allows")

    instructions.append("\n🔥 ADAPTIVE COOKING:")
    instructions.append("• Start with medium heat and adjust as needed")
    instructions.append("• Use techniques from different cuisines as appropriate")

    instructions.append("• Begin with aromatics (onions, garlic) as a flavor base")
    instructions.append("• Cook each ingredient to its optimal doneness")

    if categorized_ingredients['proteins']:
        instructions.append("• Sear protein for color and flavor development")
        instructions.append("• Remove and rest while preparing other components")

    instructions.append("• Layer flavors gradually, tasting as you go")
    instructions.append("• Combine ingredients when each is properly cooked")

    instructions.append("\n✨ CREATIVE FINISHING:")
    instructions.append("• Balance flavors - sweet, salty, sour, spicy, umami")
    instructions.append("• Add fresh elements at the end for brightness")
    instructions.append("• Present creatively, combining different cultural aesthetics")
    instructions.append("• Serve with confidence - fusion is about bold experimentation!")

    return instructions

def estimate_prep_time(categorized_ingredients):
    """Estimate preparation time based on ingredients"""
    base_time = 15  # Base 15 minutes

    # Add time for different ingredient types
    if categorized_ingredients['proteins']:
        base_time += 10  # Protein prep

    if len(categorized_ingredients['vegetables']) > 3:
        base_time += 10  # Lots of veggie prep

    if categorized_ingredients['grains']:
        base_time += 15  # Grain cooking time

    return min(base_time, 60)  # Cap at 60 minutes

def estimate_difficulty(categorized_ingredients):
    """Estimate cooking difficulty"""
    total_ingredients = sum(len(ingredients) for ingredients in categorized_ingredients.values())

    if total_ingredients <= 3:
        return 'Easy'
    elif total_ingredients <= 6:
        return 'Medium'
    else:
        return 'Hard'

def estimate_calories(categorized_ingredients):
    """Estimate calories based on ingredients"""
    calories = 0

    # Rough calorie estimates per ingredient type
    calories += len(categorized_ingredients['proteins']) * 150
    calories += len(categorized_ingredients['vegetables']) * 25
    calories += len(categorized_ingredients['grains']) * 200
    calories += len(categorized_ingredients['dairy']) * 100
    calories += len(categorized_ingredients['oils_fats']) * 120
    calories += len(categorized_ingredients['nuts_seeds']) * 180

    return max(calories, 150)  # Minimum 150 calories

def determine_cuisine_style(categorized_ingredients):
    """Determine cuisine style based on ingredients with enhanced detection"""
    all_ingredients = []
    for ingredients in categorized_ingredients.values():
        all_ingredients.extend([ing.lower() for ing in ingredients])

    ingredient_text = ' '.join(all_ingredients)

    # Enhanced cuisine detection with more specific patterns
    cuisine_patterns = {
        'Indian': [
            'cumin', 'turmeric', 'garam masala', 'curry', 'coriander', 'cardamom',
            'mustard seed', 'fenugreek', 'asafoetida', 'curry leaves', 'tamarind',
            'paneer', 'ghee', 'basmati rice', 'lentils', 'dal', 'masala', 'tandoori',
            'biryani masala', 'tandoori masala', 'pav bhaji masala', 'curry powder',
            'red chili powder', 'coriander powder', 'chat masala', 'sambar powder',
            'rasam powder', 'kitchen king masala', 'chicken masala', 'fish masala',
            'meat masala', 'sabzi masala', 'dal masala', 'mustard seeds', 'jeera',
            'haldi', 'dhania', 'hing', 'methi', 'kala namak', 'amchur'
        ],
        'Chinese': [
            'soy sauce', 'oyster sauce', 'sesame oil', 'ginger', 'star anise',
            'five spice', 'hoisin sauce', 'rice wine', 'cornstarch', 'bok choy',
            'shiitake', 'scallions', 'water chestnuts', 'bamboo shoots'
        ],
        'Italian': [
            'basil', 'oregano', 'pasta', 'parmesan', 'mozzarella', 'tomato sauce',
            'olive oil', 'garlic', 'rosemary', 'balsamic', 'prosciutto', 'pancetta',
            'risotto', 'arborio rice', 'pine nuts', 'sun-dried tomatoes'
        ],
        'American': [
            'barbecue sauce', 'maple syrup', 'bacon', 'cheddar', 'ranch',
            'hot sauce', 'corn', 'sweet potato', 'cranberries', 'pecans',
            'bourbon', 'brown sugar', 'paprika', 'chili powder'
        ],
        'Continental': [
            'butter', 'cream', 'wine', 'herbs', 'mushrooms', 'asparagus',
            'leeks', 'shallots', 'thyme', 'bay leaves', 'white sauce',
            'hollandaise', 'bechamel', 'gruyere', 'brie', 'cognac'
        ],
        'Mexican': [
            'lime', 'cilantro', 'chili', 'avocado', 'cumin', 'paprika',
            'jalapeño', 'chipotle', 'salsa', 'tortilla', 'black beans',
            'corn', 'queso', 'tomatillo', 'poblano'
        ],
        'Thai': [
            'coconut milk', 'fish sauce', 'lime leaves', 'lemongrass',
            'galangal', 'thai basil', 'bird chili', 'palm sugar',
            'tamarind', 'curry paste', 'jasmine rice'
        ],
        'Mediterranean': [
            'olive oil', 'feta', 'olives', 'lemon', 'oregano', 'mint',
            'eggplant', 'zucchini', 'chickpeas', 'tahini', 'sumac',
            'pomegranate', 'pine nuts', 'capers'
        ]
    }

    # Score each cuisine based on ingredient matches with weighted scoring
    cuisine_scores = {}
    for cuisine, patterns in cuisine_patterns.items():
        score = 0
        for pattern in patterns:
            if pattern in ingredient_text:
                # Give extra weight to distinctive spices
                if cuisine == 'Indian' and 'masala' in pattern:
                    score += 3  # Higher weight for masala-based spices
                elif cuisine == 'Indian' and pattern in ['turmeric', 'cumin', 'coriander', 'curry']:
                    score += 2  # Higher weight for core Indian spices
                else:
                    score += 1
        if score > 0:
            cuisine_scores[cuisine] = score

    # Return the cuisine with highest score, or determine based on ingredient types
    if cuisine_scores:
        max_cuisine = max(cuisine_scores, key=cuisine_scores.get)
        # Only return if the score is significant enough
        if cuisine_scores[max_cuisine] >= 2:
            return max_cuisine

    # Enhanced fallback logic based on ingredient categories
    if categorized_ingredients['spices']:
        # Check for Indian spices specifically
        indian_spices = ['masala', 'garam masala', 'turmeric', 'cumin', 'coriander', 'curry']
        if any(spice.lower() in ' '.join([ing.lower() for ing in categorized_ingredients['spices']])
               for spice in indian_spices):
            return 'Indian'

    if categorized_ingredients['proteins'] and categorized_ingredients['vegetables']:
        if categorized_ingredients['grains']:
            return 'Continental'  # Complete meal style
        else:
            return 'American'     # Simple protein + veggie
    elif categorized_ingredients['vegetables'] and categorized_ingredients['spices']:
        return 'Indian'           # Spiced vegetable dishes
    else:
        return 'Continental'      # Default elegant style

def determine_diet_type(categorized_ingredients):
    """Determine diet type based on ingredients"""
    if categorized_ingredients['proteins']:
        # Check if proteins are meat-based
        meat_proteins = ['chicken', 'beef', 'pork', 'fish', 'salmon', 'tuna', 'shrimp', 'turkey', 'lamb']
        all_proteins = ' '.join([ing.lower() for ing in categorized_ingredients['proteins']])

        if any(meat in all_proteins for meat in meat_proteins):
            return 'Non-Veg'
        else:
            return 'Vegetarian'
    else:
        return 'Vegan'

def generate_cooking_tips(categorized_ingredients, recipe_type):
    """Generate helpful cooking tips"""
    tips = []

    if categorized_ingredients['proteins']:
        tips.append("Don't overcook proteins - they can become tough and dry")

    if categorized_ingredients['vegetables']:
        tips.append("Cut vegetables uniformly for even cooking")

    if categorized_ingredients['spices']:
        tips.append("Toast whole spices briefly to enhance their flavor")

    if recipe_type == 'complete_meal':
        tips.append("Cook ingredients in order of their cooking time - harder vegetables first")

    tips.append("Taste as you go and adjust seasoning accordingly")

    return ' | '.join(tips)

def identify_ingredients_from_image(image_path):
    """Placeholder for image-based ingredient detection"""
    # This would use computer vision/ML in a real implementation
    # For now, return some common ingredients as a placeholder
    import random
    common_ingredients = ['tomato', 'onion', 'garlic', 'carrot', 'potato', 'chicken', 'rice']
    return random.sample(common_ingredients, random.randint(2, 4))

def scale_ingredient_quantity(ingredient, scale_factor):
    """Scale ingredient quantity based on serving size multiplier"""
    import re
    from fractions import Fraction

    # Common unit conversions and patterns
    units = {
        # Volume
        'cup': 'cup', 'cups': 'cup',
        'tbsp': 'tbsp', 'tablespoon': 'tbsp', 'tablespoons': 'tbsp',
        'tsp': 'tsp', 'teaspoon': 'tsp', 'teaspoons': 'tsp',
        'ml': 'ml', 'milliliter': 'ml', 'milliliters': 'ml',
        'l': 'l', 'liter': 'l', 'liters': 'l',
        # Weight
        'g': 'g', 'gram': 'g', 'grams': 'g',
        'kg': 'kg', 'kilogram': 'kg', 'kilograms': 'kg',
        'oz': 'oz', 'ounce': 'oz', 'ounces': 'oz',
        'lb': 'lb', 'pound': 'lb', 'pounds': 'lb',
        # Count
        'piece': 'piece', 'pieces': 'piece',
        'clove': 'clove', 'cloves': 'clove',
        'slice': 'slice', 'slices': 'slice',
        'can': 'can', 'cans': 'can',
        'bottle': 'bottle', 'bottles': 'bottle'
    }

    # Pattern to match quantities: number + optional fraction + unit + ingredient
    pattern = r'^(\d+(?:\.\d+)?(?:\s*/\s*\d+)?|\d+/\d+)?\s*([a-zA-Z]+)?\s+(.+)$'
    match = re.match(pattern, ingredient.strip())

    # If no match with unit, try pattern without unit
    if not match:
        pattern = r'^(\d+(?:\.\d+)?(?:\s*/\s*\d+)?|\d+/\d+)\s+(.+)$'
        match = re.match(pattern, ingredient.strip())
        if match:
            quantity_str, ingredient_name = match.groups()
            unit = None
        else:
            return ingredient  # Return as-is if no pattern matches
    else:
        quantity_str, unit, ingredient_name = match.groups()

    if not quantity_str:
        return ingredient  # No quantity to scale

    try:
        # Handle fractions and mixed numbers
        if '/' in quantity_str:
            if ' ' in quantity_str:  # Mixed number like "1 1/2"
                whole, frac = quantity_str.split(' ', 1)
                quantity = float(whole) + float(Fraction(frac))
            else:  # Pure fraction like "1/2"
                quantity = float(Fraction(quantity_str))
        else:
            quantity = float(quantity_str)

        # Scale the quantity
        scaled_quantity = quantity * scale_factor

        # Format the scaled quantity nicely
        if scaled_quantity == int(scaled_quantity):
            scaled_quantity_str = str(int(scaled_quantity))
        else:
            # Try to convert to fraction if it's a nice fraction
            frac = Fraction(scaled_quantity).limit_denominator(16)
            if abs(float(frac) - scaled_quantity) < 0.01:
                if frac.numerator > frac.denominator:
                    whole = frac.numerator // frac.denominator
                    remainder = frac.numerator % frac.denominator
                    if remainder == 0:
                        scaled_quantity_str = str(whole)
                    else:
                        scaled_quantity_str = f"{whole} {remainder}/{frac.denominator}"
                else:
                    scaled_quantity_str = str(frac)
            else:
                scaled_quantity_str = f"{scaled_quantity:.1f}".rstrip('0').rstrip('.')

        # Reconstruct the ingredient string
        if unit and unit.lower() in units:
            # Adjust unit for plurality
            base_unit = units[unit.lower()]
            if scaled_quantity > 1 and base_unit in ['cup', 'tbsp', 'tsp', 'gram', 'piece', 'clove', 'slice', 'can', 'bottle']:
                if base_unit == 'cup':
                    unit = 'cups'
                elif base_unit == 'tbsp':
                    unit = 'tbsp'  # tbsp doesn't change
                elif base_unit == 'tsp':
                    unit = 'tsp'   # tsp doesn't change
                elif base_unit == 'gram':
                    unit = 'grams' if scaled_quantity != 1 else 'gram'
                elif base_unit in ['piece', 'clove', 'slice', 'can', 'bottle']:
                    unit = base_unit + 's'

            return f"{scaled_quantity_str} {unit} {ingredient_name.strip()}"
        elif unit:
            return f"{scaled_quantity_str} {unit} {ingredient_name.strip()}"
        else:
            # Handle pluralization for ingredient names without units
            ingredient_name = ingredient_name.strip()
            if scaled_quantity > 1:
                # Dictionary of common irregular plurals
                irregular_plurals = {
                    'onion': 'onions',
                    'potato': 'potatoes',
                    'tomato': 'tomatoes',
                    'chicken': 'chicken',  # uncountable
                    'rice': 'rice',        # uncountable
                    'fish': 'fish',        # uncountable
                    'beef': 'beef',        # uncountable
                    'pork': 'pork',        # uncountable
                }

                ingredient_lower = ingredient_name.lower()
                if ingredient_lower in irregular_plurals:
                    ingredient_name = irregular_plurals[ingredient_lower]
                elif ingredient_name.endswith('o') and ingredient_name.lower() not in ['photo', 'piano']:
                    ingredient_name = ingredient_name + 'es'  # potato -> potatoes
                elif ingredient_name.endswith('y') and len(ingredient_name) > 1 and ingredient_name[-2] not in 'aeiou':
                    ingredient_name = ingredient_name[:-1] + 'ies'  # berry -> berries
                elif ingredient_name.endswith(('s', 'sh', 'ch', 'x', 'z')):
                    ingredient_name = ingredient_name + 'es'
                elif not ingredient_name.endswith('s'):
                    ingredient_name = ingredient_name + 's'

            return f"{scaled_quantity_str} {ingredient_name}"

    except (ValueError, ZeroDivisionError):
        return ingredient  # Return original if scaling fails

def scale_recipe_ingredients(ingredients_list, target_servings, base_servings=2):
    """Scale all ingredients in a recipe based on serving size"""
    if target_servings == base_servings:
        return ingredients_list

    scale_factor = target_servings / base_servings
    scaled_ingredients = []

    for ingredient in ingredients_list:
        scaled_ingredient = scale_ingredient_quantity(ingredient, scale_factor)
        scaled_ingredients.append(scaled_ingredient)

    return scaled_ingredients

def adjust_cooking_times(instructions, scale_factor):
    """Adjust cooking times in instructions based on serving size"""
    import re

    adjusted_instructions = []

    for instruction in instructions:
        # Pattern to find time mentions (e.g., "5 minutes", "1 hour", "30-45 min")
        time_pattern = r'(\d+(?:-\d+)?)\s*(minute|minutes|min|hour|hours|hr|hrs)'

        def adjust_time(match):
            time_str = match.group(1)
            unit = match.group(2)

            # Handle ranges like "30-45"
            if '-' in time_str:
                start, end = map(int, time_str.split('-'))
                # For cooking times, we don't scale linearly - use a more conservative approach
                if scale_factor > 1:
                    # Increase time by 10-20% for larger batches
                    multiplier = 1 + (scale_factor - 1) * 0.15
                else:
                    # Decrease time by 5-10% for smaller batches
                    multiplier = 1 - (1 - scale_factor) * 0.1

                new_start = max(1, int(start * multiplier))
                new_end = max(1, int(end * multiplier))
                return f"{new_start}-{new_end} {unit}"
            else:
                time_val = int(time_str)
                # Apply similar scaling logic
                if scale_factor > 1:
                    multiplier = 1 + (scale_factor - 1) * 0.15
                else:
                    multiplier = 1 - (1 - scale_factor) * 0.1

                new_time = max(1, int(time_val * multiplier))
                return f"{new_time} {unit}"

        adjusted_instruction = re.sub(time_pattern, adjust_time, instruction, flags=re.IGNORECASE)
        adjusted_instructions.append(adjusted_instruction)

    return adjusted_instructions

def extract_ingredient_quantities(ingredients_list):
    """Extract and organize ingredient quantities for use in instructions"""
    import re
    from fractions import Fraction

    quantities = {}

    for ingredient in ingredients_list:
        # Parse ingredient to extract quantity, unit, and name
        pattern = r'^(\d+(?:\.\d+)?(?:\s*/\s*\d+)?|\d+/\d+)?\s*([a-zA-Z]+)?\s+(.+)$'
        match = re.match(pattern, ingredient.strip())

        if not match:
            # Try pattern without unit
            pattern = r'^(\d+(?:\.\d+)?(?:\s*/\s*\d+)?|\d+/\d+)\s+(.+)$'
            match = re.match(pattern, ingredient.strip())
            if match:
                quantity_str, ingredient_name = match.groups()
                unit = None
            else:
                # No quantity found, store as-is
                quantities[ingredient.lower()] = {'quantity': None, 'unit': None, 'display': ingredient}
                continue
        else:
            quantity_str, unit, ingredient_name = match.groups()

        if quantity_str:
            try:
                # Handle fractions
                if '/' in quantity_str:
                    if ' ' in quantity_str:
                        whole, frac = quantity_str.split(' ', 1)
                        quantity = float(whole) + float(Fraction(frac))
                    else:
                        quantity = float(Fraction(quantity_str))
                else:
                    quantity = float(quantity_str)

                # Store quantity info
                key = ingredient_name.lower().strip()
                quantities[key] = {
                    'quantity': quantity,
                    'unit': unit,
                    'display': ingredient,
                    'quantity_str': quantity_str
                }
            except (ValueError, ZeroDivisionError):
                quantities[ingredient_name.lower().strip()] = {'quantity': None, 'unit': None, 'display': ingredient}
        else:
            quantities[ingredient_name.lower().strip()] = {'quantity': None, 'unit': None, 'display': ingredient}

    return quantities

def get_ingredient_reference(ingredient_name, quantities, fraction=1.0, default_quantity=None):
    """Get a formatted reference to an ingredient quantity for use in instructions"""
    # Try to find the ingredient in our quantities
    key = ingredient_name.lower().strip()

    # Try exact match first
    if key in quantities:
        info = quantities[key]
    else:
        # Try partial matches
        matches = [k for k in quantities.keys() if ingredient_name.lower() in k or k in ingredient_name.lower()]
        if matches:
            info = quantities[matches[0]]
        else:
            # If not found and default quantity provided, use it
            if default_quantity:
                return default_quantity
            return ingredient_name  # Return as-is if not found

    if info['quantity'] is None:
        return info['display']

    # Calculate the amount to use (fraction of total)
    amount = info['quantity'] * fraction

    # Format the amount nicely
    if amount == int(amount):
        amount_str = str(int(amount))
    else:
        from fractions import Fraction
        frac = Fraction(amount).limit_denominator(16)
        if abs(float(frac) - amount) < 0.01:
            if frac.numerator > frac.denominator:
                whole = frac.numerator // frac.denominator
                remainder = frac.numerator % frac.denominator
                if remainder == 0:
                    amount_str = str(whole)
                else:
                    amount_str = f"{whole} {remainder}/{frac.denominator}"
            else:
                amount_str = str(frac)
        else:
            amount_str = f"{amount:.1f}".rstrip('0').rstrip('.')

    # Return formatted reference
    if info['unit']:
        return f"{amount_str} {info['unit']} {key}"
    else:
        return f"{amount_str} {key}"

def get_default_cooking_quantities(servings=2):
    """Get default quantities for common cooking ingredients based on servings"""
    base_quantities = {
        'water': f"{int(0.5 * servings)} cup water",
        'salt': f"{int(1 * servings)} tsp salt",
        'pepper': f"{int(0.5 * servings)} tsp pepper",
        'garlic': f"{int(2 * servings)} cloves garlic",
        'ginger': f"{int(1 * servings)} tsp ginger",
        'oil': f"{int(2 * servings)} tbsp oil",
        'ghee': f"{int(2 * servings)} tbsp ghee",
        'butter': f"{int(2 * servings)} tbsp butter",
        'lemon juice': f"{int(1 * servings)} tbsp lemon juice",
        'vinegar': f"{int(1 * servings)} tbsp vinegar",
        'sugar': f"{int(1 * servings)} tsp sugar",
        'flour': f"{int(2 * servings)} tbsp flour",
        'cornstarch': f"{int(1 * servings)} tsp cornstarch"
    }
    return base_quantities

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
