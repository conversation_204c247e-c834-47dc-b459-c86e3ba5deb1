<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Custom Recipe - MealMind</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'organic-beige': '#F9F5ED',
                        'organic-cream': '#FDF9F0',
                        'rich-green': '#1A5E2A',
                        'soft-green': '#2D7A3D',
                        'accent-green': '#4A9B5E',
                        'warm-brown': '#8B4513',
                        'soft-gray': '#6B7280',
                        'light-sage': '#E8F5E8'
                    },
                    fontFamily: {
                        'serif': ['Playfair Display', 'serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom organic overrides -->
    <link rel="stylesheet" href="/static/organic-overrides.css">

    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🍽️</text></svg>">
</head>
<body class="bg-organic-beige font-sans text-rich-green min-h-screen">
    <!-- Header -->
    <header class="bg-organic-cream/80 backdrop-blur-sm border-b border-rich-green/10 sticky top-0 z-50">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Brand -->
                <div class="flex items-center space-x-3">
                    <img src="/static/logo.svg" alt="MealMind Logo" class="h-12 w-12">
                    <div>
                        <h1 class="text-2xl font-serif font-bold text-rich-green tracking-tight">MealMind</h1>
                        <p class="text-xs text-soft-gray font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-rich-green hover:text-soft-green transition-colors font-medium">Home</a>
                    <a href="/browse" class="text-rich-green hover:text-soft-green transition-colors font-medium">Discover</a>
                    <a href="/smart-recipes" class="text-rich-green hover:text-soft-green transition-colors font-medium border-b-2 border-rich-green">Smart Recipes</a>
                    <a href="/meal-plan" class="text-rich-green hover:text-soft-green transition-colors font-medium">Meal Plan</a>
                </div>
            </div>
        </nav>
    </header>

    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Banner -->
        <div class="bg-light-sage border border-rich-green/20 rounded-2xl p-6 mb-8">
            <div class="flex items-center space-x-3">
                <div class="text-3xl">🎉</div>
                <div>
                    <h2 class="text-xl font-serif font-bold text-rich-green">Recipe Generated Successfully!</h2>
                    <p class="text-soft-gray">Here's a custom recipe created using only your ingredients</p>
                </div>
            </div>
        </div>

        <!-- Recipe Card -->
        <div class="bg-white rounded-3xl shadow-lg overflow-hidden">
            <!-- Recipe Header -->
            <div class="bg-gradient-to-r from-rich-green to-soft-green text-white p-8">
                <div class="flex items-start justify-between">
                    <div>
                        <h1 class="text-3xl font-serif font-bold mb-2">{{ recipe.name }}</h1>
                        <p class="text-white/80 text-lg">Custom recipe using your ingredients</p>
                    </div>
                    <div class="text-right">
                        <div class="bg-white/20 rounded-full px-4 py-2 mb-2">
                            <span class="text-sm font-medium">{{ recipe.cuisine }} Style</span>
                        </div>
                        <div class="bg-white/20 rounded-full px-4 py-2">
                            <span class="text-sm font-medium">{{ recipe.diet_type }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recipe Info -->
            <div class="p-8">
                <div class="grid md:grid-cols-4 gap-6 mb-8">
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">⏱️</span>
                        </div>
                        <div class="text-sm text-soft-gray">Prep Time</div>
                        <div class="font-semibold text-rich-green">{{ recipe.prep_time }} min</div>
                    </div>
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">👥</span>
                        </div>
                        <div class="text-sm text-soft-gray">Servings</div>
                        <div class="font-semibold text-rich-green">{{ recipe.servings }}</div>
                    </div>
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">🔥</span>
                        </div>
                        <div class="text-sm text-soft-gray">Calories</div>
                        <div class="font-semibold text-rich-green">{{ recipe.calories }}</div>
                    </div>
                    <div class="text-center">
                        <div class="bg-organic-cream rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                            <span class="text-2xl">📊</span>
                        </div>
                        <div class="text-sm text-soft-gray">Difficulty</div>
                        <div class="font-semibold text-rich-green">{{ recipe.difficulty }}</div>
                    </div>
                </div>

                <!-- Enhanced Nutrition Analysis -->
                <div class="mb-8">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">📊</span>
                        Enhanced Nutrition Analysis
                    </h3>
                    <div class="bg-light-sage rounded-2xl p-6">
                        <div id="nutrition-loading" class="text-center py-4">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-rich-green"></div>
                            <p class="mt-2 text-soft-gray">Analyzing nutrition...</p>
                        </div>
                        <div id="nutrition-content" style="display: none;">
                            <!-- Nutrition content will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Recipe Ingredients -->
                <div class="mb-8">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">🥗</span>
                        Ingredients ({{ recipe.servings }} serving{{ 's' if recipe.servings != 1 else '' }})
                    </h3>
                    <div class="bg-light-sage rounded-2xl p-6">
                        <div class="grid gap-3">
                            {% if recipe.ingredients.provided %}
                                {% for ingredient in recipe.ingredients.provided %}
                                <div class="flex items-center space-x-3 bg-white rounded-lg p-3 border border-rich-green/10">
                                    <div class="w-2 h-2 bg-rich-green rounded-full flex-shrink-0"></div>
                                    <span class="text-rich-green font-medium">{{ ingredient }}</span>
                                </div>
                                {% endfor %}
                            {% else %}
                                {% for ingredient in user_ingredients %}
                                <div class="flex items-center space-x-3 bg-white rounded-lg p-3 border border-rich-green/10">
                                    <div class="w-2 h-2 bg-rich-green rounded-full flex-shrink-0"></div>
                                    <span class="text-rich-green font-medium">{{ ingredient }}</span>
                                </div>
                                {% endfor %}
                            {% endif %}
                        </div>

                        {% if recipe.servings != 2 %}
                        <div class="mt-4 p-3 bg-accent-green/10 rounded-lg border border-accent-green/20">
                            <div class="text-sm text-accent-green font-medium flex items-center">
                                <span class="text-lg mr-2">📏</span>
                                Quantities adjusted for {{ recipe.servings }} serving{{ 's' if recipe.servings != 1 else '' }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Instructions -->
                <div class="mb-8">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">👨‍🍳</span>
                        Cooking Instructions
                    </h3>

                    {% if recipe.servings != 2 %}
                    <div class="mb-4 p-3 bg-soft-green/10 rounded-lg border border-soft-green/20">
                        <div class="text-sm text-soft-green font-medium flex items-center">
                            <span class="text-lg mr-2">⏱️</span>
                            Cooking times have been adjusted for {{ recipe.servings }} serving{{ 's' if recipe.servings != 1 else '' }}
                        </div>
                    </div>
                    {% endif %}

                    <div class="space-y-4">
                        {% for instruction in recipe.instructions %}
                        <div class="flex items-start space-x-4">
                            <div class="bg-rich-green text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                                {{ loop.index }}
                            </div>
                            <p class="text-soft-gray leading-relaxed">{{ instruction }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Cooking Tips -->
                {% if recipe.tips %}
                <div class="mb-8">
                    <h3 class="text-xl font-serif font-bold text-rich-green mb-4 flex items-center">
                        <span class="text-2xl mr-2">💡</span>
                        Cooking Tips
                    </h3>
                    <div class="bg-organic-cream rounded-2xl p-6">
                        <p class="text-soft-gray leading-relaxed">{{ recipe.tips }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-rich-green/10">
                    <button onclick="window.print()" class="bg-rich-green text-white px-6 py-3 rounded-full font-semibold hover:bg-soft-green transition-colors flex items-center justify-center">
                        <span class="mr-2">🖨️</span>
                        Print Recipe
                    </button>
                    <a href="/smart-recipes" class="border-2 border-rich-green text-rich-green px-6 py-3 rounded-full font-semibold hover:bg-rich-green hover:text-white transition-colors text-center">
                        Generate Another Recipe
                    </a>
                    <a href="/browse" class="text-rich-green hover:text-soft-green transition-colors px-6 py-3 font-medium text-center">
                        Browse More Recipes
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-rich-green text-organic-beige py-12 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <img src="/static/logo.svg" alt="MealMind Logo" class="h-10 w-10">
                <div>
                    <h3 class="text-2xl font-serif font-bold">MealMind</h3>
                    <p class="text-sm text-organic-beige/80 font-medium tracking-wider uppercase">Think Smart. Eat Better.</p>
                </div>
            </div>
            <p class="text-organic-beige/60 text-sm">
                © 2025 MealMind. Made with 💚 for food lovers everywhere.
            </p>
        </div>
    </footer>

    <script src="/static/script.js"></script>

    <script>
        // Enhanced Nutrition Analysis
        document.addEventListener('DOMContentLoaded', function() {
            loadEnhancedNutrition();
        });

        async function loadEnhancedNutrition() {
            try {
                // Get ingredients from the recipe
                const ingredients = [
                    {% if recipe.ingredients.provided %}
                        {% for ingredient in recipe.ingredients.provided %}
                            "{{ ingredient|safe }}",
                        {% endfor %}
                    {% else %}
                        {% for ingredient in user_ingredients %}
                            "{{ ingredient|safe }}",
                        {% endfor %}
                    {% endif %}
                ];

                const servings = {{ recipe.servings }};

                const response = await fetch('/api/php/nutrition', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ingredients, servings })
                });

                const data = await response.json();

                if (data.success) {
                    displayEnhancedNutrition(data);
                } else {
                    showNutritionError('Failed to analyze nutrition');
                }
            } catch (error) {
                console.error('Nutrition analysis error:', error);
                showNutritionError('Unable to load nutrition analysis');
            }
        }

        function displayEnhancedNutrition(data) {
            const loadingDiv = document.getElementById('nutrition-loading');
            const contentDiv = document.getElementById('nutrition-content');

            const nutrition = data.nutrition;
            const dailyValues = data.daily_values;
            const insights = data.insights;

            contentDiv.innerHTML = `
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
                    <div class="bg-white rounded-lg p-4 text-center border border-rich-green/10">
                        <div class="text-2xl font-bold text-rich-green">${nutrition.calories}</div>
                        <div class="text-sm text-soft-gray">Calories</div>
                        <div class="text-xs text-accent-green">${dailyValues.calories}% DV</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-rich-green/10">
                        <div class="text-2xl font-bold text-rich-green">${nutrition.protein}g</div>
                        <div class="text-sm text-soft-gray">Protein</div>
                        <div class="text-xs text-accent-green">${dailyValues.protein}% DV</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-rich-green/10">
                        <div class="text-2xl font-bold text-rich-green">${nutrition.carbs}g</div>
                        <div class="text-sm text-soft-gray">Carbs</div>
                        <div class="text-xs text-accent-green">${dailyValues.carbs}% DV</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-rich-green/10">
                        <div class="text-2xl font-bold text-rich-green">${nutrition.fat}g</div>
                        <div class="text-sm text-soft-gray">Fat</div>
                        <div class="text-xs text-accent-green">${dailyValues.fat}% DV</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-rich-green/10">
                        <div class="text-2xl font-bold text-rich-green">${nutrition.fiber}g</div>
                        <div class="text-sm text-soft-gray">Fiber</div>
                        <div class="text-xs text-accent-green">${dailyValues.fiber}% DV</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-rich-green/10">
                        <div class="text-2xl font-bold text-rich-green">${nutrition.sodium}mg</div>
                        <div class="text-sm text-soft-gray">Sodium</div>
                        <div class="text-xs text-accent-green">${dailyValues.sodium}% DV</div>
                    </div>
                </div>

                ${insights.length > 0 ? `
                    <div class="bg-white rounded-lg p-4 border border-rich-green/10">
                        <h4 class="font-semibold text-rich-green mb-3 flex items-center">
                            <span class="text-lg mr-2">💡</span>
                            Nutrition Insights
                        </h4>
                        <div class="space-y-2">
                            ${insights.map(insight => `
                                <div class="flex items-start space-x-2">
                                    <div class="w-2 h-2 bg-accent-green rounded-full mt-2 flex-shrink-0"></div>
                                    <p class="text-soft-gray text-sm">${insight}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <div class="mt-4 text-center">
                    <p class="text-xs text-soft-gray">
                        Enhanced nutrition analysis powered by PHP backend | Per serving (${data.servings} servings)
                    </p>
                </div>
            `;

            loadingDiv.style.display = 'none';
            contentDiv.style.display = 'block';
        }

        function showNutritionError(message) {
            const loadingDiv = document.getElementById('nutrition-loading');
            const contentDiv = document.getElementById('nutrition-content');

            contentDiv.innerHTML = `
                <div class="bg-white rounded-lg p-4 text-center border border-rich-green/10">
                    <div class="text-soft-gray">
                        <span class="text-2xl">⚠️</span>
                        <p class="mt-2">${message}</p>
                    </div>
                </div>
            `;

            loadingDiv.style.display = 'none';
            contentDiv.style.display = 'block';
        }
    </script>
</body>
</html>
