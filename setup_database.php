<?php
/**
 * MealMind Database Setup Script
 * Creates the necessary database tables for the PHP backend
 */

define('MEALMIND_ACCESS', true);
require_once 'config.php';

echo "MealMind Database Setup\n";
echo "======================\n\n";

try {
    // Connect to MySQL server (without database)
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);

    echo "✓ Connected to MySQL server\n";

    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Database '" . DB_NAME . "' created/verified\n";

    // Connect to the specific database
    $pdo->exec("USE `" . DB_NAME . "`");

    // Create tables
    createTables($pdo);

    // Insert sample data
    insertSampleData($pdo);

    echo "\n✅ Database setup completed successfully!\n";

} catch (PDOException $e) {
    echo "\n❌ Database setup failed: " . $e->getMessage() . "\n";
    exit(1);
}

function createTables($pdo) {
    echo "\nCreating tables...\n";
    
    // Users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
    ");
    echo "  ✓ Users table created\n";
    
    // User preferences table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_preferences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            favorite_cuisines JSON,
            dietary_preferences JSON,
            allergies JSON,
            cooking_skill ENUM('Beginner', 'Intermediate', 'Advanced', 'Expert') DEFAULT 'Beginner',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    echo "  ✓ User preferences table created\n";
    
    // Recipes table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS recipes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            cuisine VARCHAR(50),
            diet_type VARCHAR(50),
            prep_time INT, -- in minutes
            cook_time INT, -- in minutes
            servings INT DEFAULT 4,
            difficulty ENUM('Easy', 'Medium', 'Hard') DEFAULT 'Easy',
            calories INT,
            ingredients JSON,
            instructions JSON,
            nutrition_info JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
    ");
    echo "  ✓ Recipes table created\n";
    
    // Recipe ratings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS recipe_ratings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            recipe_id INT NOT NULL,
            user_id INT NOT NULL,
            rating DECIMAL(2,1) CHECK (rating >= 1 AND rating <= 5),
            review TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_recipe (user_id, recipe_id)
        )
    ");
    echo "  ✓ Recipe ratings table created\n";
    
    // Meal plans table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS meal_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            recipe_id INT NOT NULL,
            meal_date DATE NOT NULL,
            meal_type ENUM('breakfast', 'lunch', 'dinner', 'snack') NOT NULL,
            servings INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE,
            UNIQUE KEY unique_meal_plan (user_id, meal_date, meal_type)
        )
    ");
    echo "  ✓ Meal plans table created\n";
    
    // Nutrition analysis cache table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS nutrition_cache (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ingredients_hash VARCHAR(64) UNIQUE NOT NULL,
            ingredients JSON NOT NULL,
            nutrition_data JSON NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL
        )
    ");
    echo "  ✓ Nutrition cache table created\n";
    
    // Cooking tips table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cooking_tips (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category VARCHAR(50) NOT NULL,
            title VARCHAR(200) NOT NULL,
            content TEXT NOT NULL,
            difficulty_level ENUM('Beginner', 'Intermediate', 'Advanced', 'All') DEFAULT 'All',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "  ✓ Cooking tips table created\n";
    
    // Ingredient substitutions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS ingredient_substitutions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_ingredient VARCHAR(100) NOT NULL,
            substitute_ingredient VARCHAR(100) NOT NULL,
            ratio VARCHAR(100) NOT NULL,
            notes TEXT,
            category VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "  ✓ Ingredient substitutions table created\n";
}

function insertSampleData($pdo) {
    echo "\nInserting sample data...\n";
    
    // Sample cooking tips
    $tips = [
        ['general', 'Sharp Knives are Safer', 'A sharp knife is actually safer than a dull one because it requires less pressure and is less likely to slip.', 'All'],
        ['general', 'Mise en Place', 'Always prepare and organize all your ingredients before you start cooking. This French term means "everything in its place".', 'All'],
        ['baking', 'Room Temperature Ingredients', 'For baking, use room temperature eggs, butter, and dairy unless the recipe specifically calls for cold ingredients.', 'Beginner'],
        ['grilling', 'Preheat Your Grill', 'Always preheat your grill for 10-15 minutes before cooking to ensure even heat distribution.', 'Beginner'],
        ['general', 'Taste as You Go', 'Season and taste your food throughout the cooking process, not just at the end.', 'All']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO cooking_tips (category, title, content, difficulty_level) VALUES (?, ?, ?, ?)");
    foreach ($tips as $tip) {
        $stmt->execute($tip);
    }
    echo "  ✓ Sample cooking tips inserted\n";
    
    // Sample ingredient substitutions
    $substitutions = [
        ['butter', 'vegetable oil', '3/4 the amount', 'For baking and cooking', 'baking'],
        ['butter', 'applesauce', 'equal amount', 'For baking only, reduces fat content', 'baking'],
        ['eggs', 'flax egg', '1 tbsp ground flaxseed + 3 tbsp water per egg', 'Vegan substitute, let sit for 5 minutes', 'vegan'],
        ['milk', 'almond milk', 'equal amount', 'Dairy-free alternative', 'dairy-free'],
        ['heavy cream', 'milk + butter', '3/4 cup milk + 1/4 cup melted butter', 'Mix well before using', 'general'],
        ['brown sugar', 'white sugar + molasses', '1 cup white sugar + 2 tbsp molasses', 'Mix thoroughly', 'baking'],
        ['buttermilk', 'milk + vinegar', '1 cup milk + 1 tbsp white vinegar', 'Let sit for 5 minutes', 'baking']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO ingredient_substitutions (original_ingredient, substitute_ingredient, ratio, notes, category) VALUES (?, ?, ?, ?, ?)");
    foreach ($substitutions as $sub) {
        $stmt->execute($sub);
    }
    echo "  ✓ Sample ingredient substitutions inserted\n";
    
    // Sample recipes
    $recipes = [
        [
            'name' => 'Classic Chicken Stir Fry',
            'description' => 'A quick and healthy chicken stir fry with fresh vegetables',
            'cuisine' => 'Chinese',
            'diet_type' => 'Regular',
            'prep_time' => 15,
            'cook_time' => 10,
            'servings' => 4,
            'difficulty' => 'Easy',
            'calories' => 320,
            'ingredients' => json_encode([
                '1 lb chicken breast, sliced thin',
                '2 tbsp vegetable oil',
                '1 bell pepper, sliced',
                '1 onion, sliced',
                '2 cloves garlic, minced',
                '2 tbsp soy sauce',
                '1 tbsp cornstarch',
                '1 tsp ginger, grated'
            ]),
            'instructions' => json_encode([
                'Heat oil in a large wok or skillet over high heat',
                'Add chicken and cook until no longer pink, about 5 minutes',
                'Add vegetables and stir-fry for 3-4 minutes',
                'Mix soy sauce and cornstarch, add to pan',
                'Cook until sauce thickens, about 1 minute',
                'Serve immediately over rice'
            ])
        ],
        [
            'name' => 'Vegetarian Pasta Primavera',
            'description' => 'Fresh seasonal vegetables with pasta in a light cream sauce',
            'cuisine' => 'Italian',
            'diet_type' => 'Vegetarian',
            'prep_time' => 20,
            'cook_time' => 15,
            'servings' => 4,
            'difficulty' => 'Easy',
            'calories' => 380,
            'ingredients' => json_encode([
                '12 oz pasta',
                '2 tbsp olive oil',
                '1 zucchini, sliced',
                '1 bell pepper, sliced',
                '1 cup cherry tomatoes',
                '3 cloves garlic, minced',
                '1/2 cup heavy cream',
                '1/2 cup parmesan cheese',
                'Fresh basil leaves'
            ]),
            'instructions' => json_encode([
                'Cook pasta according to package directions',
                'Heat olive oil in a large pan',
                'Sauté vegetables until tender-crisp',
                'Add garlic and cook for 1 minute',
                'Add cream and simmer for 2 minutes',
                'Toss with pasta and parmesan',
                'Garnish with fresh basil'
            ])
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO recipes (name, description, cuisine, diet_type, prep_time, cook_time, servings, difficulty, calories, ingredients, instructions) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($recipes as $recipe) {
        $stmt->execute([
            $recipe['name'],
            $recipe['description'],
            $recipe['cuisine'],
            $recipe['diet_type'],
            $recipe['prep_time'],
            $recipe['cook_time'],
            $recipe['servings'],
            $recipe['difficulty'],
            $recipe['calories'],
            $recipe['ingredients'],
            $recipe['instructions']
        ]);
    }
    echo "  ✓ Sample recipes inserted\n";
}

?>
