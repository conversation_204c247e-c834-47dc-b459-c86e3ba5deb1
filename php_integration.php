<?php
/**
 * MealMind PHP Integration
 * Provides PHP backend services for the MealMind Flask application
 */

define('MEALMIND_ACCESS', true);
require_once 'config.php';

// Enable CORS for cross-origin requests
header('Access-Control-Allow-Origin: http://127.0.0.1:5001');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight OPTIONS requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get the request path and method
$request_uri = $_SERVER['REQUEST_URI'];
$request_method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove the script name from path
$path = str_replace('/php_integration.php', '', $path);

// Route the request
switch ($path) {
    case '/php/nutrition':
        handleNutritionAnalysis();
        break;
    
    case '/php/preferences':
        handleUserPreferences();
        break;
    
    case '/php/ratings':
        handleRecipeRatings();
        break;
    
    case '/php/tips':
        handleCookingTips();
        break;
    
    case '/php/substitutions':
        handleIngredientSubstitutions();
        break;
    
    case '/php/health':
        handleHealthCheck();
        break;
    
    default:
        jsonResponse(['error' => 'PHP endpoint not found', 'path' => $path], 404);
}

/**
 * Health check endpoint
 */
function handleHealthCheck() {
    jsonResponse([
        'status' => 'healthy',
        'service' => 'MealMind PHP Backend',
        'version' => APP_VERSION,
        'timestamp' => date('Y-m-d H:i:s'),
        'features' => [
            'nutrition_analysis' => FEATURE_NUTRITION_ANALYSIS,
            'recipe_suggestions' => FEATURE_RECIPE_SUGGESTIONS,
            'user_ratings' => FEATURE_USER_RATINGS,
            'cooking_tips' => FEATURE_COOKING_TIPS
        ]
    ]);
}

/**
 * Enhanced nutrition analysis with PHP processing
 */
function handleNutritionAnalysis() {
    global $request_method;
    
    if ($request_method !== 'POST') {
        errorResponse('Method not allowed', 405);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $ingredients = $input['ingredients'] ?? [];
    $servings = $input['servings'] ?? 1;
    
    if (empty($ingredients)) {
        errorResponse('Ingredients are required');
    }
    
    // Enhanced nutrition database with more detailed information
    $nutrition_db = [
        'chicken' => [
            'calories' => 165, 'protein' => 31, 'carbs' => 0, 'fat' => 3.6,
            'fiber' => 0, 'sugar' => 0, 'sodium' => 74, 'cholesterol' => 85,
            'vitamin_a' => 21, 'vitamin_c' => 1.6, 'calcium' => 15, 'iron' => 1
        ],
        'rice' => [
            'calories' => 130, 'protein' => 2.7, 'carbs' => 28, 'fat' => 0.3,
            'fiber' => 0.4, 'sugar' => 0.1, 'sodium' => 5, 'cholesterol' => 0,
            'vitamin_a' => 0, 'vitamin_c' => 0, 'calcium' => 28, 'iron' => 0.8
        ],
        'tomato' => [
            'calories' => 18, 'protein' => 0.9, 'carbs' => 3.9, 'fat' => 0.2,
            'fiber' => 1.2, 'sugar' => 2.6, 'sodium' => 5, 'cholesterol' => 0,
            'vitamin_a' => 833, 'vitamin_c' => 13.7, 'calcium' => 10, 'iron' => 0.3
        ],
        'onion' => [
            'calories' => 40, 'protein' => 1.1, 'carbs' => 9.3, 'fat' => 0.1,
            'fiber' => 1.7, 'sugar' => 4.2, 'sodium' => 4, 'cholesterol' => 0,
            'vitamin_a' => 2, 'vitamin_c' => 7.4, 'calcium' => 23, 'iron' => 0.2
        ],
        'oil' => [
            'calories' => 884, 'protein' => 0, 'carbs' => 0, 'fat' => 100,
            'fiber' => 0, 'sugar' => 0, 'sodium' => 0, 'cholesterol' => 0,
            'vitamin_a' => 0, 'vitamin_c' => 0, 'calcium' => 0, 'iron' => 0
        ],
        'garlic' => [
            'calories' => 149, 'protein' => 6.4, 'carbs' => 33, 'fat' => 0.5,
            'fiber' => 2.1, 'sugar' => 1, 'sodium' => 17, 'cholesterol' => 0,
            'vitamin_a' => 9, 'vitamin_c' => 31.2, 'calcium' => 181, 'iron' => 1.7
        ]
    ];
    
    $total_nutrition = [
        'calories' => 0, 'protein' => 0, 'carbs' => 0, 'fat' => 0,
        'fiber' => 0, 'sugar' => 0, 'sodium' => 0, 'cholesterol' => 0,
        'vitamin_a' => 0, 'vitamin_c' => 0, 'calcium' => 0, 'iron' => 0
    ];
    
    $matched_ingredients = [];
    
    foreach ($ingredients as $ingredient) {
        $ingredient_lower = strtolower(trim($ingredient));
        
        // Extract quantity and unit (enhanced parsing)
        preg_match('/(\d+(?:\.\d+)?)\s*(\w+)?\s+(.+)/', $ingredient, $matches);
        $quantity = isset($matches[1]) ? floatval($matches[1]) : 1;
        $unit = isset($matches[2]) ? $matches[2] : 'unit';
        $ingredient_name = isset($matches[3]) ? trim($matches[3]) : $ingredient_lower;
        
        // Find matching ingredient in database
        foreach ($nutrition_db as $db_ingredient => $nutrition) {
            if (strpos($ingredient_name, $db_ingredient) !== false) {
                $multiplier = calculateMultiplier($quantity, $unit, $db_ingredient);
                
                foreach ($nutrition as $nutrient => $value) {
                    $total_nutrition[$nutrient] += $value * $multiplier;
                }
                
                $matched_ingredients[] = [
                    'ingredient' => $ingredient,
                    'matched_as' => $db_ingredient,
                    'quantity' => $quantity,
                    'unit' => $unit
                ];
                break;
            }
        }
    }
    
    // Calculate per serving and round values
    foreach ($total_nutrition as $nutrient => $value) {
        $total_nutrition[$nutrient] = round($value / $servings, 1);
    }
    
    // Calculate daily value percentages (based on 2000 calorie diet)
    $daily_values = [
        'calories' => round(($total_nutrition['calories'] / 2000) * 100, 1),
        'protein' => round(($total_nutrition['protein'] / 50) * 100, 1),
        'carbs' => round(($total_nutrition['carbs'] / 300) * 100, 1),
        'fat' => round(($total_nutrition['fat'] / 65) * 100, 1),
        'fiber' => round(($total_nutrition['fiber'] / 25) * 100, 1),
        'sodium' => round(($total_nutrition['sodium'] / 2300) * 100, 1)
    ];
    
    // Generate nutrition insights
    $insights = generateNutritionInsights($total_nutrition, $daily_values);
    
    jsonResponse([
        'success' => true,
        'nutrition' => $total_nutrition,
        'daily_values' => $daily_values,
        'servings' => $servings,
        'matched_ingredients' => $matched_ingredients,
        'insights' => $insights,
        'processed_by' => 'PHP Backend'
    ]);
}

/**
 * Calculate multiplier based on quantity and unit
 */
function calculateMultiplier($quantity, $unit, $ingredient) {
    // Base multiplier (assuming nutrition data is per 100g)
    $base_multiplier = $quantity / 100;
    
    // Adjust based on unit
    switch (strtolower($unit)) {
        case 'kg':
            return $base_multiplier * 10;
        case 'lb':
            return $base_multiplier * 4.54; // 1 lb = 454g
        case 'oz':
            return $base_multiplier * 0.284; // 1 oz = 28.4g
        case 'cup':
            // Different ingredients have different cup weights
            $cup_weights = [
                'rice' => 185,
                'chicken' => 140,
                'onion' => 160
            ];
            $weight = $cup_weights[$ingredient] ?? 120;
            return ($quantity * $weight) / 100;
        case 'tbsp':
            return ($quantity * 15) / 100; // 1 tbsp ≈ 15g
        case 'tsp':
            return ($quantity * 5) / 100; // 1 tsp ≈ 5g
        default:
            return $base_multiplier;
    }
}

/**
 * Generate nutrition insights
 */
function generateNutritionInsights($nutrition, $daily_values) {
    $insights = [];
    
    if ($nutrition['protein'] > 20) {
        $insights[] = "High in protein - great for muscle building and satiety";
    }
    
    if ($nutrition['fiber'] > 5) {
        $insights[] = "Good source of fiber - supports digestive health";
    }
    
    if ($nutrition['vitamin_c'] > 15) {
        $insights[] = "Rich in Vitamin C - boosts immune system";
    }
    
    if ($nutrition['sodium'] > 600) {
        $insights[] = "High in sodium - consider reducing salt or using herbs for flavor";
    }
    
    if ($nutrition['calories'] < 300) {
        $insights[] = "Low calorie option - perfect for weight management";
    }
    
    if ($daily_values['fat'] > 30) {
        $insights[] = "High in fat - consider using cooking methods that require less oil";
    }
    
    return $insights;
}

/**
 * User preferences management
 */
function handleUserPreferences() {
    global $request_method;
    
    if ($request_method === 'GET') {
        $user_id = $_GET['user_id'] ?? 'default';
        $preferences = loadUserPreferences($user_id);
        jsonResponse(['success' => true, 'preferences' => $preferences]);
        
    } elseif ($request_method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $user_id = $input['user_id'] ?? 'default';
        $preferences = $input['preferences'] ?? [];
        
        saveUserPreferences($user_id, $preferences);
        jsonResponse(['success' => true, 'message' => 'Preferences saved successfully']);
    } else {
        errorResponse('Method not allowed', 405);
    }
}

/**
 * Load user preferences from file or database
 */
function loadUserPreferences($user_id) {
    $file_path = DATA_DIR . "preferences_{$user_id}.json";
    
    if (file_exists($file_path)) {
        return json_decode(file_get_contents($file_path), true);
    }
    
    // Return default preferences
    return [
        'favorite_cuisines' => ['Italian', 'Chinese', 'Indian'],
        'dietary_restrictions' => [],
        'allergies' => [],
        'cooking_skill' => 'Beginner',
        'preferred_meal_time' => 30,
        'spice_level' => 'Medium'
    ];
}

/**
 * Save user preferences to file or database
 */
function saveUserPreferences($user_id, $preferences) {
    $file_path = DATA_DIR . "preferences_{$user_id}.json";
    $preferences['updated_at'] = date('Y-m-d H:i:s');
    return file_put_contents($file_path, json_encode($preferences, JSON_PRETTY_PRINT));
}

/**
 * Recipe ratings management
 */
function handleRecipeRatings() {
    global $request_method;
    
    if ($request_method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $recipe_id = $input['recipe_id'] ?? null;
        $user_id = $input['user_id'] ?? 'anonymous';
        $rating = $input['rating'] ?? null;
        $review = $input['review'] ?? '';
        
        if (!$recipe_id || !$rating) {
            errorResponse('Recipe ID and rating are required');
        }
        
        if ($rating < 1 || $rating > 5) {
            errorResponse('Rating must be between 1 and 5');
        }
        
        saveRating($recipe_id, $user_id, $rating, $review);
        jsonResponse(['success' => true, 'message' => 'Rating saved successfully']);
        
    } elseif ($request_method === 'GET') {
        $recipe_id = $_GET['recipe_id'] ?? null;
        
        if (!$recipe_id) {
            errorResponse('Recipe ID is required');
        }
        
        $ratings = getRatings($recipe_id);
        jsonResponse(['success' => true, 'ratings' => $ratings]);
    } else {
        errorResponse('Method not allowed', 405);
    }
}

/**
 * Save rating to file
 */
function saveRating($recipe_id, $user_id, $rating, $review) {
    $ratings_file = DATA_DIR . 'ratings.json';
    $ratings = file_exists($ratings_file) ? json_decode(file_get_contents($ratings_file), true) : [];
    
    $ratings[] = [
        'recipe_id' => $recipe_id,
        'user_id' => $user_id,
        'rating' => $rating,
        'review' => $review,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    file_put_contents($ratings_file, json_encode($ratings, JSON_PRETTY_PRINT));
}

/**
 * Get ratings for a recipe
 */
function getRatings($recipe_id) {
    $ratings_file = DATA_DIR . 'ratings.json';
    
    if (!file_exists($ratings_file)) {
        return ['average' => 0, 'count' => 0, 'ratings' => []];
    }
    
    $all_ratings = json_decode(file_get_contents($ratings_file), true);
    $recipe_ratings = array_filter($all_ratings, function($rating) use ($recipe_id) {
        return $rating['recipe_id'] == $recipe_id;
    });
    
    $count = count($recipe_ratings);
    $average = $count > 0 ? array_sum(array_column($recipe_ratings, 'rating')) / $count : 0;
    
    return [
        'average' => round($average, 1),
        'count' => $count,
        'ratings' => array_values($recipe_ratings)
    ];
}

/**
 * Cooking tips endpoint
 */
function handleCookingTips() {
    $category = $_GET['category'] ?? 'general';
    $difficulty = $_GET['difficulty'] ?? 'All';
    
    $tips = getCookingTips($category, $difficulty);
    jsonResponse(['success' => true, 'tips' => $tips, 'category' => $category]);
}

/**
 * Get cooking tips
 */
function getCookingTips($category, $difficulty) {
    // This would normally come from database, using static data for now
    $all_tips = [
        'general' => [
            ['title' => 'Sharp Knives', 'content' => 'A sharp knife is safer and more efficient than a dull one.', 'difficulty' => 'All'],
            ['title' => 'Mise en Place', 'content' => 'Prepare all ingredients before cooking.', 'difficulty' => 'All'],
            ['title' => 'Taste as You Go', 'content' => 'Season and taste throughout cooking.', 'difficulty' => 'All']
        ],
        'baking' => [
            ['title' => 'Room Temperature', 'content' => 'Use room temperature ingredients for better mixing.', 'difficulty' => 'Beginner'],
            ['title' => 'Measure Accurately', 'content' => 'Baking requires precise measurements.', 'difficulty' => 'All']
        ]
    ];
    
    $tips = $all_tips[$category] ?? $all_tips['general'];
    
    if ($difficulty !== 'All') {
        $tips = array_filter($tips, function($tip) use ($difficulty) {
            return $tip['difficulty'] === $difficulty || $tip['difficulty'] === 'All';
        });
    }
    
    return array_values($tips);
}

/**
 * Ingredient substitutions endpoint
 */
function handleIngredientSubstitutions() {
    $ingredient = $_GET['ingredient'] ?? '';
    
    if (empty($ingredient)) {
        errorResponse('Ingredient parameter is required');
    }
    
    $substitutions = getSubstitutions($ingredient);
    jsonResponse(['success' => true, 'substitutions' => $substitutions, 'ingredient' => $ingredient]);
}

/**
 * Get ingredient substitutions
 */
function getSubstitutions($ingredient) {
    $substitutions_db = [
        'butter' => [
            ['substitute' => 'vegetable oil', 'ratio' => '3/4 the amount', 'notes' => 'For cooking and baking'],
            ['substitute' => 'applesauce', 'ratio' => 'equal amount', 'notes' => 'For baking only, reduces fat']
        ],
        'eggs' => [
            ['substitute' => 'flax egg', 'ratio' => '1 tbsp ground flaxseed + 3 tbsp water per egg', 'notes' => 'Vegan option'],
            ['substitute' => 'applesauce', 'ratio' => '1/4 cup per egg', 'notes' => 'For baking']
        ],
        'milk' => [
            ['substitute' => 'almond milk', 'ratio' => 'equal amount', 'notes' => 'Dairy-free option'],
            ['substitute' => 'coconut milk', 'ratio' => 'equal amount', 'notes' => 'Rich, creamy texture']
        ]
    ];
    
    $ingredient_lower = strtolower(trim($ingredient));
    
    foreach ($substitutions_db as $key => $subs) {
        if (strpos($ingredient_lower, $key) !== false) {
            return $subs;
        }
    }
    
    return [];
}

?>
